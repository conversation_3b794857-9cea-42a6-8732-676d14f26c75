# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
.temp.md
*.temp.md

# Archive folder
.archive/
# Build outputs
dist/
.tsbuildinfo
*.tsbuildinfo
# Allow build directory but not compiled outputs
build/**/*.js
build/**/*.js.map
build/**/*.d.ts
!build/docker/
!build/make/
*.js
!jest.config.js
!jest.setup.js
!eslint.config.js
!tests/**/*.js
*.js.map
*.d.ts
!src/**/*.d.ts

# Environment files
.env
.env.local
.env.*.local
.edgerc
edgerc-clean.txt
claude_desktop_config.json

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS files
.DS_Store
Thumbs.db

# Test coverage
coverage/
.nyc_output/

# Test results
.testresults/

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
*.tmp
*.temp
nginx/

# MCP server configs (user-specific)
.mcp.json.local

# Memory files
memory-complete.md
akamai-api-memory.md

#Claude things
.claude
.prompts
.old

# SBOM outputs (can be regenerated)
sbom/
*.sbom.json
*.sbom.xml

#Akamai API Credentials
.edgerc
.edgerc*.bak
*.backup
*.d.ts.map

# Personalized Claude Desktop configs
claude_desktop_config_*.json
!examples/claude_desktop_config*.example.json
CLAUDE.MD

# Security and credentials
.secrets/
credentials/
*.private
*.secret
*.key
*.pem
*.cert
.tokens/
tokens/
*.token

# Docker
docker-compose.override.yml
.dockerignore

# Akamai specific
.akamai/
akamai-logs/
purge-cache/

# MCP specific
mcp-state/
.mcp-cache/

# Documentation build
docs/_build/
docs/.doctrees/

# Local development
.local/
local-test/
scratch/