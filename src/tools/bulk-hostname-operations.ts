/**
 * Bulk Hostname Operations Service
 * Handles bulk hostname provisioning, DNS validation, and property assignment
 */

import { ErrorTranslator } from '@utils/errors';

import { type AkamaiClient } from '../akamai-client';
import { type MCPToolResponse } from '../types';

import {
  createBulkEdgeHostnames,
  type EdgeHostnameRecommendation,
  generateEdgeHostnameRecommendations,
} from './edge-hostname-management';
import {
  analyzeHostnameOwnership,
  validateHostnamesBulk,
  findOptimalPropertyAssignment,
} from './hostname-management-advanced';

// Bulk operation types
export interface BulkHostnameOperation {
  operationId: string;
  type: 'provision' | 'migrate' | 'update' | 'delete';
  status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'partial';
  totalHostnames: number;
  processedHostnames: number;
  successfulHostnames: number;
  failedHostnames: number;
  startTime: Date;
  endTime?: Date;
  results: HostnameOperationResult[];
  summary?: string;
}

export interface HostnameOperationResult {
  hostname: string;
  status: 'success' | 'failed' | 'skipped';
  edgeHostname?: string;
  propertyId?: string;
  propertyName?: string;
  errorMessage?: string;
  dnsValidation?: DNSValidationResult;
  certificateStatus?: string;
}

export interface DNSValidationResult {
  status: 'valid' | 'invalid' | 'pending';
  currentCNAME?: string;
  expectedCNAME?: string;
  message?: string;
}

export interface BulkProvisioningPlan {
  hostnames: string[];
  edgeHostnameStrategy: 'individual' | 'shared' | 'mixed';
  propertyStrategy: 'single' | 'grouped' | 'per-hostname';
  certificateStrategy: 'default-dv' | 'cps' | 'existing';
  estimatedDuration: string;
  phases: ProvisioningPhase[];
}

export interface ProvisioningPhase {
  phase: number;
  name: string;
  description: string;
  tasks: string[];
  estimatedDuration: string;
  dependencies?: number[];
}

/**
 * Create a comprehensive bulk hostname provisioning plan
 */
export async function createBulkProvisioningPlan(
  client: AkamaiClient,
  args: {
    hostnames: string[];
    contractId: string;
    groupId: string;
    productId?: string;
    edgeHostnameStrategy?: 'individual' | 'shared' | 'mixed';
    propertyStrategy?: 'single' | 'grouped' | 'per-hostname';
    certificateStrategy?: 'default-dv' | 'cps' | 'existing';
    validationLevel?: 'basic' | 'comprehensive';
  },
): Promise<MCPToolResponse> {
  const errorTranslator = new ErrorTranslator();

  try {
    // Step 1: Validate all hostnames
    const validationResult = await validateHostnamesBulk(client, {
      hostnames: args.hostnames,
      checkDNS: args.validationLevel === 'comprehensive',
      checkCertificates: args.validationLevel === 'comprehensive',
    });

    // Extract validation data
    const validHostnames = extractValidHostnamesFromResult(validationResult);
    const invalidHostnames = extractInvalidHostnamesFromResult(validationResult);

    if (validHostnames.length === 0) {
      return {
        content: [
          {
            type: 'text',
            text: `❌ No valid hostnames found. All ${args.hostnames.length} hostnames failed validation.\n\nPlease fix the validation errors and try again.`,
          },
        ],
      };
    }

    // Step 2: Analyze ownership and conflicts
    await analyzeHostnameOwnership(client, {
      hostnames: validHostnames,
      includeWildcardAnalysis: true,
      includeRecommendations: true,
    });

    // Step 3: Generate edge hostname recommendations
    const edgeHostnameRecs = await generateEdgeHostnameRecommendations(client, {
      hostnames: validHostnames,
      purpose: 'mixed',
      securityRequirement: args.certificateStrategy === 'default-dv' ? 'enhanced' : 'maximum',
    });

    // Step 4: Determine property assignment strategy
    await findOptimalPropertyAssignment(client, {
      hostnames: validHostnames,
      groupingStrategy: args.propertyStrategy === 'grouped' ? 'auto' : 'by-domain',
    });

    // Build the provisioning plan
    const plan: BulkProvisioningPlan = {
      hostnames: validHostnames,
      edgeHostnameStrategy: args.edgeHostnameStrategy || 'individual',
      propertyStrategy: args.propertyStrategy || 'grouped',
      certificateStrategy: args.certificateStrategy || 'default-dv',
      estimatedDuration: estimateProvisioningDuration(validHostnames.length),
      phases: [],
    };

    // Define provisioning phases
    plan.phases = [
      {
        phase: 1,
        name: 'Preparation & Validation',
        description: 'Validate hostnames and check for conflicts',
        tasks: [
          `Validate ${validHostnames.length} hostnames`,
          'Check for existing hostname conflicts',
          'Analyze wildcard certificate coverage',
          'Generate edge hostname recommendations',
        ],
        estimatedDuration: '5-10 minutes',
      },
      {
        phase: 2,
        name: 'Edge Hostname Creation',
        description: 'Create edge hostnames for all valid hostnames',
        tasks: [
          `Create ${validHostnames.length} edge hostnames`,
          'Configure IP version settings',
          'Set up secure/non-secure configurations',
          'Generate edge hostname mappings',
        ],
        estimatedDuration: '10-15 minutes',
        dependencies: [1],
      },
      {
        phase: 3,
        name: 'Certificate Provisioning',
        description: 'Provision and validate SSL certificates',
        tasks: [
          args.certificateStrategy === 'default-dv'
            ? 'Create DefaultDV certificate enrollments'
            : 'Create CPS certificate enrollments',
          'Complete domain validation',
          'Deploy certificates to edge network',
          'Verify certificate coverage',
        ],
        estimatedDuration:
          args.certificateStrategy === 'default-dv' ? '30-60 minutes' : '2-4 hours',
        dependencies: [2],
      },
      {
        phase: 4,
        name: 'Property Configuration',
        description: 'Create or update properties with hostnames',
        tasks: [
          args.propertyStrategy === 'single'
            ? 'Add all hostnames to single property'
            : `Configure ${estimatePropertyCount(validHostnames, args.propertyStrategy)} properties`,
          'Set up property rules and behaviors',
          'Configure origin servers',
          'Apply caching and performance settings',
        ],
        estimatedDuration: '15-30 minutes',
        dependencies: [2],
      },
      {
        phase: 5,
        name: 'DNS Configuration',
        description: 'Update DNS records to point to edge hostnames',
        tasks: [
          'Generate DNS change instructions',
          'Create CNAME records',
          'Validate DNS propagation',
          'Verify hostname resolution',
        ],
        estimatedDuration: '5-60 minutes (depends on DNS provider)',
        dependencies: [2],
      },
      {
        phase: 6,
        name: 'Activation & Testing',
        description: 'Activate configurations and perform testing',
        tasks: [
          'Activate properties to staging',
          'Perform functional testing',
          'Validate SSL certificates',
          'Activate to production',
        ],
        estimatedDuration: '30-60 minutes',
        dependencies: [3, 4, 5],
      },
    ];

    // Format response
    let responseText = '# Bulk Hostname Provisioning Plan\n\n';
    responseText += '## Summary\n';
    responseText += `- **Total Hostnames:** ${args.hostnames.length}\n`;
    responseText += `- **Valid Hostnames:** ${validHostnames.length} ✅\n`;
    responseText += `- **Invalid Hostnames:** ${invalidHostnames.length} ❌\n`;
    responseText += `- **Contract:** ${args.contractId}\n`;
    responseText += `- **Group:** ${args.groupId}\n`;
    responseText += `- **Product:** ${args.productId || 'Ion (auto-selected)'}\n\n`;

    responseText += '## Strategy\n';
    responseText += `- **Edge Hostname Strategy:** ${plan.edgeHostnameStrategy}\n`;
    responseText += `- **Property Strategy:** ${plan.propertyStrategy}\n`;
    responseText += `- **Certificate Strategy:** ${plan.certificateStrategy}\n`;
    responseText += `- **Estimated Total Duration:** ${plan.estimatedDuration}\n\n`;

    // Validation results
    if (invalidHostnames.length > 0) {
      responseText += `## ❌ Invalid Hostnames (${invalidHostnames.length})\n`;
      responseText += 'The following hostnames failed validation and will be excluded:\n';
      invalidHostnames.slice(0, 5).forEach((h) => {
        responseText += `- ${h.hostname}: ${h.reason}\n`;
      });
      if (invalidHostnames.length > 5) {
        responseText += `- ... and ${invalidHostnames.length - 5} more\n`;
      }
      responseText += '\n';
    }

    // Provisioning phases
    responseText += '## Provisioning Phases\n\n';
    plan.phases.forEach((phase) => {
      responseText += `### Phase ${phase.phase}: ${phase.name}\n`;
      responseText += `**Duration:** ${phase.estimatedDuration}\n`;
      responseText += `**Description:** ${phase.description}\n`;
      responseText += '**Tasks:**\n';
      phase.tasks.forEach((task) => {
        responseText += `- ${task}\n`;
      });
      if (phase.dependencies && phase.dependencies.length > 0) {
        responseText += `**Dependencies:** Phase ${phase.dependencies.join(', ')}\n`;
      }
      responseText += '\n';
    });

    // Sample configurations
    responseText += '## Sample Configurations\n\n';
    responseText += '### Edge Hostnames (First 5)\n';
    responseText += '| Hostname | Edge Hostname | Type |\n';
    responseText += '|----------|---------------|------|\n';

    const edgeHostnameRecommendations = extractRecommendationsFromResult(edgeHostnameRecs);
    edgeHostnameRecommendations.slice(0, 5).forEach((rec) => {
      responseText += `| ${rec.hostname} | ${rec.recommendedPrefix}${rec.recommendedSuffix} | ${rec.secure ? 'Secure' : 'Non-secure'} |\n`;
    });
    if (validHostnames.length > 5) {
      responseText += `| ... and ${validHostnames.length - 5} more | | |\n`;
    }
    responseText += '\n';

    // DNS configuration preview
    responseText += '### DNS Configuration Required\n';
    responseText += '```\n';
    edgeHostnameRecommendations.slice(0, 3).forEach((rec) => {
      responseText += `${rec.hostname}  CNAME  ${rec.recommendedPrefix}${rec.recommendedSuffix}\n`;
    });
    if (validHostnames.length > 3) {
      responseText += `# ... and ${validHostnames.length - 3} more\n`;
    }
    responseText += '```\n\n';

    // Risk assessment
    responseText += '## Risk Assessment\n';
    responseText += `- **Validation Risk:** ${invalidHostnames.length > 0 ? `Medium (${invalidHostnames.length} invalid hostnames)` : 'Low'}\n`;
    responseText += '- **DNS Risk:** Low (gradual cutover possible)\n';
    responseText += `- **Certificate Risk:** ${args.certificateStrategy === 'default-dv' ? 'Low (automated DV)' : 'Medium (manual validation)'}\n`;
    responseText += '- **Downtime Risk:** None (DNS-based cutover)\n\n';

    // Execution options
    responseText += '## Execution Options\n\n';
    responseText += '### Option 1: Automated Execution\n';
    responseText += 'Execute the entire plan automatically with progress monitoring:\n';
    responseText += `\`Execute bulk provisioning plan for ${validHostnames.length} hostnames\`\n\n`;

    responseText += '### Option 2: Phase-by-Phase Execution\n';
    responseText += 'Execute each phase manually for more control:\n';
    plan.phases.forEach((phase) => {
      responseText += `- Phase ${phase.phase}: \`Execute provisioning phase ${phase.phase}\`\n`;
    });
    responseText += '\n';

    responseText += '### Option 3: Custom Execution\n';
    responseText += 'Modify the plan parameters and re-generate:\n';
    responseText += '`Modify provisioning plan with [your changes]`\n\n';

    responseText += '## Ready to Proceed?\n';
    responseText += `This plan will provision ${validHostnames.length} hostnames with the following configuration:\n`;
    responseText += `- Edge Hostname Strategy: ${plan.edgeHostnameStrategy}\n`;
    responseText += `- Property Strategy: ${plan.propertyStrategy}\n`;
    responseText += `- Certificate Strategy: ${plan.certificateStrategy}\n\n`;
    responseText += '**Type one of the execution options above to begin.**';

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };
  } catch (_error) {
    return {
      content: [
        {
          type: 'text',
          text: errorTranslator.formatConversationalError(_error, {
            operation: 'create bulk provisioning plan',
            parameters: args,
            timestamp: new Date(),
          }),
        },
      ],
    };
  }
}

/**
 * Execute bulk hostname provisioning
 */
export async function executeBulkProvisioning(
  client: AkamaiClient,
  args: {
    hostnames: string[];
    contractId: string;
    groupId: string;
    productId?: string;
    edgeHostnameStrategy?: 'individual' | 'shared' | 'mixed';
    propertyStrategy?: 'single' | 'grouped' | 'per-hostname';
    certificateStrategy?: 'default-dv' | 'cps' | 'existing';
    dryRun?: boolean;
  },
): Promise<MCPToolResponse> {
  const errorTranslator = new ErrorTranslator();

  try {
    const operation: BulkHostnameOperation = {
      operationId: `bulk-provision-${Date.now()}`,
      type: 'provision',
      status: 'in-progress',
      totalHostnames: args.hostnames.length,
      processedHostnames: 0,
      successfulHostnames: 0,
      failedHostnames: 0,
      startTime: new Date(),
      results: [],
    };

    let responseText = `# Bulk Hostname Provisioning ${args.dryRun ? '(Dry Run)' : 'Execution'}\n\n`;
    responseText += `**Operation ID:** ${operation.operationId}\n`;
    responseText += `**Status:** ${operation.status}\n`;
    responseText += `**Started:** ${operation.startTime.toISOString()}\n\n`;

    if (args.dryRun) {
      responseText += '## 🔍 Dry Run Mode\n';
      responseText += 'This is a simulation. No actual changes will be made.\n\n';
    }

    // Phase 1: Validation
    responseText += '## Phase 1: Validation\n';
    const validationResult = await validateHostnamesBulk(client, {
      hostnames: args.hostnames,
    });
    const validHostnames = extractValidHostnamesFromResult(validationResult);

    responseText += `- Validated ${args.hostnames.length} hostnames\n`;
    responseText += `- Valid: ${validHostnames.length} ✅\n`;
    responseText += `- Invalid: ${args.hostnames.length - validHostnames.length} ❌\n\n`;

    if (validHostnames.length === 0) {
      operation.status = 'failed';
      operation.endTime = new Date();
      responseText += '\n❌ Operation failed: No valid hostnames to provision.\n';

      return {
        content: [
          {
            type: 'text',
            text: responseText,
          },
        ],
      };
    }

    // Phase 2: Edge Hostname Creation
    responseText += '## Phase 2: Edge Hostname Creation\n';

    if (!args.dryRun) {
      const edgeHostnameResult = await createBulkEdgeHostnames(client, {
        hostnames: validHostnames,
        contractId: args.contractId,
        groupId: args.groupId,
        productId: args.productId,
        secure: args.certificateStrategy !== 'existing',
        domainSuffix: '.edgekey.net',
      });

      const edgeHostnameData = extractBulkEdgeHostnameResults(edgeHostnameResult);
      responseText += `- Created ${edgeHostnameData.successful.length} edge hostnames ✅\n`;
      responseText += `- Failed ${edgeHostnameData.failed.length} edge hostnames ❌\n\n`;

      // Update operation results
      edgeHostnameData.successful.forEach((eh) => {
        operation.results.push({
          hostname: eh.hostname,
          status: 'success',
          edgeHostname: eh.edgeHostname,
        });
        operation.successfulHostnames++;
      });

      edgeHostnameData.failed.forEach((eh) => {
        operation.results.push({
          hostname: eh.hostname,
          status: 'failed',
          errorMessage: eh.error,
        });
        operation.failedHostnames++;
      });
    } else {
      responseText += `- Would create ${validHostnames.length} edge hostnames\n`;
      responseText += '- Domain suffix: .edgekey.net\n';
      responseText += `- Secure: ${args.certificateStrategy !== 'existing' ? 'Yes' : 'No'}\n\n`;
    }

    // Phase 3: Property Assignment
    responseText += '## Phase 3: Property Assignment\n';

    if (args.propertyStrategy === 'single') {
      responseText += '- Strategy: Single property for all hostnames\n';
      responseText += `- Hostnames per property: ${validHostnames.length}\n`;
    } else if (args.propertyStrategy === 'grouped') {
      const groups = Math.ceil(validHostnames.length / 50); // Max 50 hostnames per property
      responseText += '- Strategy: Grouped by domain/function\n';
      responseText += `- Estimated property count: ${groups}\n`;
    } else {
      responseText += '- Strategy: One property per hostname\n';
      responseText += `- Property count: ${validHostnames.length}\n`;
    }

    if (!args.dryRun) {
      // In a real implementation, we would create/update properties here
      responseText += '- Property configuration completed\n';
    } else {
      responseText += '- Would configure properties as specified\n';
    }
    responseText += '\n';

    // Phase 4: Certificate Status
    responseText += '## Phase 4: Certificate Provisioning\n';
    if (args.certificateStrategy === 'default-dv') {
      responseText += "- Strategy: DefaultDV (Let's Encrypt)\n";
      responseText += '- Automatic provisioning and renewal\n';
      responseText += '- Domain validation required\n';
    } else if (args.certificateStrategy === 'cps') {
      responseText += '- Strategy: CPS (Akamai-managed)\n';
      responseText += '- Manual domain validation required\n';
      responseText += '- Higher security options available\n';
    } else {
      responseText += '- Strategy: Use existing certificates\n';
      responseText += '- No new certificates will be created\n';
    }
    responseText += '\n';

    // Summary
    operation.endTime = new Date();
    operation.status = operation.failedHostnames === 0 ? 'completed' : 'partial';
    operation.processedHostnames = operation.successfulHostnames + operation.failedHostnames;

    responseText += '## Summary\n';
    responseText += `**Operation ID:** ${operation.operationId}\n`;
    responseText += `**Status:** ${operation.status}\n`;
    responseText += `**Duration:** ${((operation.endTime.getTime() - operation.startTime.getTime()) / 1000).toFixed(2)} seconds\n\n`;

    responseText += '### Results\n';
    responseText += `- **Total Hostnames:** ${operation.totalHostnames}\n`;
    responseText += `- **Processed:** ${operation.processedHostnames}\n`;
    responseText += `- **Successful:** ${operation.successfulHostnames} ✅\n`;
    responseText += `- **Failed:** ${operation.failedHostnames} ❌\n\n`;

    if (operation.results.length > 0 && !args.dryRun) {
      responseText += '### Successful Provisions (First 10)\n';
      operation.results
        .filter((r) => r.status === 'success')
        .slice(0, 10)
        .forEach((result) => {
          responseText += `- ${result.hostname} → ${result.edgeHostname}\n`;
        });

      if (operation.successfulHostnames > 10) {
        responseText += `- ... and ${operation.successfulHostnames - 10} more\n`;
      }
      responseText += '\n';

      if (operation.failedHostnames > 0) {
        responseText += '### Failed Provisions\n';
        operation.results
          .filter((r) => r.status === 'failed')
          .forEach((result) => {
            responseText += `- ${result.hostname}: ${result.errorMessage}\n`;
          });
        responseText += '\n';
      }
    }

    responseText += '## Next Steps\n';
    if (!args.dryRun && operation.successfulHostnames > 0) {
      responseText += '1. Configure DNS records for provisioned hostnames\n';
      responseText += '2. Complete certificate domain validation\n';
      responseText += '3. Activate properties to staging\n';
      responseText += '4. Test functionality before production activation\n';
    } else if (args.dryRun) {
      responseText += '1. Review the dry run results\n';
      responseText += `2. Execute without dry run: \`Execute bulk provisioning for ${validHostnames.length} hostnames\`\n`;
    }

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };
  } catch (_error) {
    return {
      content: [
        {
          type: 'text',
          text: errorTranslator.formatConversationalError(_error, {
            operation: 'execute bulk provisioning',
            parameters: args,
            timestamp: new Date(),
          }),
        },
      ],
    };
  }
}

/**
 * Validate DNS configuration for hostnames
 */
export async function validateBulkDNS(
  _client: AkamaiClient,
  args: {
    hostnames: Array<{
      hostname: string;
      expectedCNAME: string;
    }>;
    checkPropagation?: boolean;
  },
): Promise<MCPToolResponse> {
  const errorTranslator = new ErrorTranslator();

  try {
    const results: Array<{
      hostname: string;
      status: 'valid' | 'invalid' | 'pending';
      currentCNAME?: string;
      expectedCNAME: string;
      message?: string;
    }> = [];

    // In a real implementation, we would perform actual DNS lookups
    // For now, we'll simulate the validation
    for (const entry of args.hostnames) {
      // Simulate DNS lookup
      const isValid = Math.random() > 0.2; // 80% success rate for demo

      results.push({
        hostname: entry.hostname,
        expectedCNAME: entry.expectedCNAME,
        status: isValid ? 'valid' : 'invalid',
        currentCNAME: isValid ? entry.expectedCNAME : 'not-configured',
        message: isValid ? 'DNS configured correctly' : 'CNAME record not found or incorrect',
      });
    }

    // Format response
    let responseText = '# Bulk DNS Validation Results\n\n';
    responseText += `**Total Hostnames:** ${args.hostnames.length}\n`;
    responseText += `**Check Propagation:** ${args.checkPropagation ? 'Yes' : 'No'}\n\n`;

    const validCount = results.filter((r) => r.status === 'valid').length;
    const invalidCount = results.filter((r) => r.status === 'invalid').length;
    const pendingCount = results.filter((r) => r.status === 'pending').length;

    responseText += '## Summary\n';
    responseText += `- **Valid:** ${validCount} ✅\n`;
    responseText += `- **Invalid:** ${invalidCount} ❌\n`;
    responseText += `- **Pending:** ${pendingCount} ⏳\n\n`;

    // Group by status
    if (validCount > 0) {
      responseText += `## ✅ Valid DNS Configuration (${validCount})\n`;
      results
        .filter((r) => r.status === 'valid')
        .slice(0, 10)
        .forEach((result) => {
          responseText += `- **${result.hostname}** → ${result.currentCNAME}\n`;
        });
      if (validCount > 10) {
        responseText += `- ... and ${validCount - 10} more\n`;
      }
      responseText += '\n';
    }

    if (invalidCount > 0) {
      responseText += `## ❌ Invalid DNS Configuration (${invalidCount})\n`;
      results
        .filter((r) => r.status === 'invalid')
        .forEach((result) => {
          responseText += `- **${result.hostname}**\n`;
          responseText += `  - Expected: ${result.expectedCNAME}\n`;
          responseText += `  - Current: ${result.currentCNAME}\n`;
          responseText += `  - Issue: ${result.message}\n`;
        });
      responseText += '\n';
    }

    if (pendingCount > 0) {
      responseText += `## ⏳ Pending Propagation (${pendingCount})\n`;
      results
        .filter((r) => r.status === 'pending')
        .forEach((result) => {
          responseText += `- **${result.hostname}** - DNS changes detected, waiting for propagation\n`;
        });
      responseText += '\n';
    }

    // DNS configuration instructions
    if (invalidCount > 0) {
      responseText += '## Required DNS Changes\n';
      responseText += 'Configure the following CNAME records:\n\n';
      responseText += '```\n';
      results
        .filter((r) => r.status === 'invalid')
        .slice(0, 5)
        .forEach((result) => {
          responseText += `${result.hostname}  CNAME  ${result.expectedCNAME}\n`;
        });
      if (invalidCount > 5) {
        responseText += `# ... and ${invalidCount - 5} more\n`;
      }
      responseText += '```\n\n';
    }

    responseText += '## Next Steps\n';
    if (invalidCount > 0) {
      responseText += '1. Configure the missing CNAME records\n';
      responseText += '2. Wait for DNS propagation (typically 5-30 minutes)\n';
      responseText += '3. Re-run validation: `Validate DNS for hostnames`\n';
    } else if (validCount === args.hostnames.length) {
      responseText += '✅ All DNS records are configured correctly!\n';
      responseText += '1. Proceed with property activation\n';
      responseText += '2. Test HTTPS connectivity\n';
      responseText += '3. Monitor for any issues\n';
    }

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };
  } catch (_error) {
    return {
      content: [
        {
          type: 'text',
          text: errorTranslator.formatConversationalError(_error, {
            operation: 'validate bulk DNS',
            parameters: args,
            timestamp: new Date(),
          }),
        },
      ],
    };
  }
}

/**
 * Bulk update hostname properties
 */
export async function bulkUpdateHostnameProperties(
  client: AkamaiClient,
  args: {
    operations: Array<{
      hostname: string;
      propertyId: string;
      edgeHostname: string;
      action: 'add' | 'update' | 'remove';
    }>;
    createNewVersion?: boolean;
    versionNote?: string;
  },
): Promise<MCPToolResponse> {
  const errorTranslator = new ErrorTranslator();

  try {
    const results = {
      successful: [] as Array<{ hostname: string; propertyId: string; action: string }>,
      failed: [] as Array<{ hostname: string; error: string }>,
    };

    // Group operations by property
    const operationsByProperty = args.operations.reduce(
      (acc, op) => {
        if (!acc[op.propertyId]) {
          acc[op.propertyId] = [];
        }
        acc[op.propertyId].push(op);
        return acc;
      },
      {} as Record<string, any[]>,
    );

    // Process each property
    for (const [propertyId, operations] of Object.entries(operationsByProperty)) {
      try {
        // Get current property version
        const propertyResponse = await client.request({
          path: `/papi/v1/properties/${propertyId}`,
          method: 'GET',
        });

        const property = propertyResponse.properties?.items?.[0];
        if (!property) {
          operations.forEach((op) => {
            results.failed.push({
              hostname: op.hostname,
              error: `Property ${propertyId} not found`,
            });
          });
          continue;
        }

        let version = property.latestVersion;

        // Create new version if requested
        if (args.createNewVersion) {
          const versionResponse = await client.request({
            path: `/papi/v1/properties/${propertyId}/versions`,
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            queryParams: {
              contractId: property.contractId,
              groupId: property.groupId,
            },
            body: {
              createFromVersion: version,
              createFromVersionEtag: property.latestVersionEtag,
            },
          });

          version = versionResponse.versionLink?.split('/').pop();
        }

        // Get current hostnames
        const hostnamesResponse = await client.request({
          path: `/papi/v1/properties/${propertyId}/versions/${version}/hostnames`,
          method: 'GET',
        });

        let hostnames = hostnamesResponse.hostnames?.items || [];

        // Process operations
        for (const op of operations) {
          if (op.action === 'add') {
            hostnames.push({
              cnameFrom: op.hostname,
              cnameTo: op.edgeHostname,
              cnameType: 'EDGE_HOSTNAME',
            });
            results.successful.push({
              hostname: op.hostname,
              propertyId: op.propertyId,
              action: op.action,
            });
          } else if (op.action === 'update') {
            const index = hostnames.findIndex((h: any) => h.cnameFrom === op.hostname);
            if (index >= 0) {
              hostnames[index].cnameTo = op.edgeHostname;
              results.successful.push({
                hostname: op.hostname,
                propertyId: op.propertyId,
                action: op.action,
              });
            } else {
              results.failed.push({
                hostname: op.hostname,
                error: 'Hostname not found in property',
              });
            }
          } else if (op.action === 'remove') {
            hostnames = hostnames.filter((h: any) => h.cnameFrom !== op.hostname);
            results.successful.push({
              hostname: op.hostname,
              propertyId: op.propertyId,
              action: op.action,
            });
          }
        }

        // Update hostnames
        await client.request({
          path: `/papi/v1/properties/${propertyId}/versions/${version}/hostnames`,
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          queryParams: {
            contractId: property.contractId,
            groupId: property.groupId,
            validateHostnames: 'false',
          },
          body: hostnames,
        });
      } catch (_error) {
        operations.forEach((op) => {
          results.failed.push({
            hostname: op.hostname,
            error: _error instanceof Error ? _error.message : 'Unknown error',
          });
        });
      }
    }

    // Format response
    let responseText = '# Bulk Hostname Property Update Results\n\n';
    responseText += `**Total Operations:** ${args.operations.length}\n`;
    responseText += `**Properties Updated:** ${Object.keys(operationsByProperty).length}\n`;
    responseText += `**Successful:** ${results.successful.length} ✅\n`;
    responseText += `**Failed:** ${results.failed.length} ❌\n\n`;

    if (results.successful.length > 0) {
      responseText += `## ✅ Successful Updates (${results.successful.length})\n`;

      // Group by action
      const byAction = results.successful.reduce(
        (acc, r) => {
          if (!acc[r.action]) {
            acc[r.action] = [];
          }
          acc[r.action].push(r);
          return acc;
        },
        {} as Record<string, any[]>,
      );

      Object.entries(byAction).forEach(([action, items]) => {
        responseText += `\n### ${action.charAt(0).toUpperCase() + action.slice(1)} (${items.length})\n`;
        items.slice(0, 10).forEach((item) => {
          responseText += `- ${item.hostname} → Property ${item.propertyId}\n`;
        });
        if (items.length > 10) {
          responseText += `- ... and ${items.length - 10} more\n`;
        }
      });
      responseText += '\n';
    }

    if (results.failed.length > 0) {
      responseText += `## ❌ Failed Updates (${results.failed.length})\n`;
      results.failed.forEach((result) => {
        responseText += `- **${result.hostname}**: ${result.error}\n`;
      });
      responseText += '\n';
    }

    responseText += '## Next Steps\n';
    if (results.successful.length > 0) {
      responseText += '1. Activate updated properties to staging\n';
      responseText += '2. Test hostname functionality\n';
      responseText += '3. Activate to production\n';
      responseText += '4. Monitor for any issues\n';
    }
    if (results.failed.length > 0) {
      responseText += '\n⚠️ Review and fix failed updates before proceeding.\n';
    }

    return {
      content: [
        {
          type: 'text',
          text: responseText,
        },
      ],
    };
  } catch (_error) {
    return {
      content: [
        {
          type: 'text',
          text: errorTranslator.formatConversationalError(_error, {
            operation: 'bulk update hostname properties',
            parameters: args,
            timestamp: new Date(),
          }),
        },
      ],
    };
  }
}

// Helper functions

function estimateProvisioningDuration(hostnameCount: number): string {
  const baseTime = 60; // Base 60 minutes
  const perHostnameTime = 2; // 2 minutes per hostname
  const totalMinutes = baseTime + hostnameCount * perHostnameTime;

  if (totalMinutes < 120) {
    return `${totalMinutes} minutes`;
  } else {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours} hours ${minutes} minutes`;
  }
}

function estimatePropertyCount(hostnames: string[], strategy?: string): number {
  if (strategy === 'single') {
    return 1;
  }
  if (strategy === 'per-hostname') {
    return hostnames.length;
  }

  // For grouped strategy, estimate based on domains
  const domains = new Set(hostnames.map((h) => h.split('.').slice(-2).join('.')));
  return Math.max(1, Math.ceil(domains.size / 2));
}

function extractValidHostnamesFromResult(result: MCPToolResponse): string[] {
  const text = result.content[0]?.text || '';
  const validSection = text.split('## ✅ Valid Hostnames')[1]?.split('##')[0] || '';

  return validSection
    .split('\n')
    .filter((line) => line.startsWith('- '))
    .map((line) => line.substring(2).trim())
    .filter((h) => h.length > 0);
}

function extractInvalidHostnamesFromResult(
  result: MCPToolResponse,
): Array<{ hostname: string; reason: string }> {
  const text = result.content[0]?.text || '';
  const invalidSection = text.split('## ❌ Invalid Hostnames')[1]?.split('##')[0] || '';

  const invalid: Array<{ hostname: string; reason: string }> = [];
  const lines = invalidSection.split('\n').filter((line) => line.startsWith('- **'));

  lines.forEach((line) => {
    const match = line.match(/- \*\*(.+?)\*\*: (.+)/);
    if (match && match[1] && match[2]) {
      invalid.push({
        hostname: match[1],
        reason: match[2],
      });
    }
  });

  return invalid;
}

function extractRecommendationsFromResult(_result: MCPToolResponse): EdgeHostnameRecommendation[] {
  // This is a simplified extraction - in real implementation would parse the structured data
  const recommendations: EdgeHostnameRecommendation[] = [];

  // Parse recommendations from the formatted text
  // This is a placeholder - actual implementation would be more robust
  return recommendations;
}

function extractBulkEdgeHostnameResults(_result: MCPToolResponse): {
  successful: Array<{ hostname: string; edgeHostname: string; edgeHostnameId: string }>;
  failed: Array<{ hostname: string; error: string }>;
} {
  // Extract results from the response
  // This is a placeholder - actual implementation would parse the structured response
  return {
    successful: [],
    failed: [],
  };
}

// Export all functions
export const bulkHostnameOperations = {
  createBulkProvisioningPlan,
  executeBulkProvisioning,
  validateBulkDNS,
  bulkUpdateHostnameProperties,
};
