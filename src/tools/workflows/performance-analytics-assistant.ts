/**
 * Performance & Analytics Domain Assistant
 * <PERSON>'s UX Transformation: Making performance data actionable for business decisions
 * 
 * "Performance metrics are meaningless without business context. This assistant translates
 * milliseconds and cache rates into customer experience and revenue impact."
 */

import { Tool } from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import { logger } from '../../utils/logger';
import { WorkflowOrchestrator } from '../consolidated/workflow-orchestrator';

/**
 * Business Performance Context
 * Maps business goals to performance metrics
 */
export interface BusinessPerformanceContext {
  business_goal: 'conversion_rate' | 'user_engagement' | 'cost_reduction' | 'global_expansion' | 'mobile_experience';
  current_pain_points: ('slow_pages' | 'high_bounce' | 'cart_abandonment' | 'poor_mobile' | 'high_costs')[];
  performance_baseline: 'not_measured' | 'poor' | 'average' | 'good' | 'excellent';
  traffic_patterns: 'steady' | 'seasonal' | 'viral_spikes' | 'growing_rapidly';
  key_metrics: ('page_speed' | 'availability' | 'conversion' | 'user_satisfaction' | 'cost_per_user')[];
}

/**
 * Performance & Analytics Assistant
 * Translates technical metrics to business outcomes
 */
export const performanceAnalyticsAssistant: Tool = {
  name: 'performance',
  description: 'Maya\'s Performance assistant for ALECS - I turn numbers into insights that drive business decisions',
  inputSchema: {
    type: 'object',
    properties: {
      intent: {
        type: 'string',
        description: 'What performance goal do you have? (e.g., "Speed up checkout", "Reduce costs", "Improve mobile experience")',
      },
      context: {
        type: 'object',
        description: 'Business context for performance optimization',
        properties: {
          business_goal: {
            type: 'string',
            enum: ['conversion_rate', 'user_engagement', 'cost_reduction', 'global_expansion', 'mobile_experience'],
          },
          target_improvement: {
            type: 'string',
            description: 'What improvement are you targeting? (e.g., "20% faster", "50% cost reduction")',
          },
          critical_pages: {
            type: 'array',
            items: { type: 'string' },
            description: 'Your most important pages/paths',
          },
        },
      },
      timeframe: {
        type: 'string',
        description: 'Analysis timeframe',
        enum: ['real_time', 'last_hour', 'last_day', 'last_week', 'last_month', 'custom'],
      },
      compare_to: {
        type: 'string',
        description: 'Comparison period for trending',
        enum: ['previous_period', 'same_time_last_week', 'same_time_last_month', 'baseline'],
      },
    },
    required: ['intent'],
  },
};

/**
 * Performance Impact Calculators
 * Convert technical metrics to business impact
 */
const IMPACT_CALCULATORS = {
  page_speed: {
    metric: 'Page Load Time',
    businessImpact: (currentMs: number, improvedMs: number, monthlyVisitors: number) => {
      // Amazon found every 100ms delay costs 1% in sales
      const improvementMs = currentMs - improvedMs;
      const conversionImpact = (improvementMs / 100) * 0.01;
      const additionalConversions = monthlyVisitors * conversionImpact * 0.02; // 2% base conversion
      
      return {
        technicalImprovement: `${improvementMs}ms faster`,
        businessImpact: `+${(conversionImpact * 100).toFixed(1)}% conversion rate`,
        estimatedValue: `${Math.round(additionalConversions)} additional conversions/month`,
        userExperience: improvementMs > 1000 ? 'Significant UX improvement' : 'Noticeable improvement',
      };
    },
  },
  mobile_performance: {
    metric: 'Mobile Experience Score',
    businessImpact: (currentScore: number, improvedScore: number, mobileTrafficPercent: number) => {
      // Google: 53% of mobile users abandon sites that take >3s to load
      const scoreImprovement = improvedScore - currentScore;
      const bounceReduction = scoreImprovement * 0.5; // 0.5% per point
      const affectedTraffic = mobileTrafficPercent / 100;
      
      return {
        technicalImprovement: `+${scoreImprovement} points`,
        businessImpact: `-${bounceReduction}% mobile bounce rate`,
        estimatedValue: `${(bounceReduction * affectedTraffic).toFixed(1)}% more engaged users`,
        userExperience: scoreImprovement > 20 ? 'Transformative mobile experience' : 'Better mobile experience',
      };
    },
  },
  availability: {
    metric: 'Uptime',
    businessImpact: (currentUptime: number, improvedUptime: number, revenuePerHour: number) => {
      const downtimeReduction = (100 - currentUptime) - (100 - improvedUptime);
      const hoursPerYear = 8760;
      const savedHours = (downtimeReduction / 100) * hoursPerYear;
      const savedRevenue = savedHours * revenuePerHour;
      
      return {
        technicalImprovement: `${downtimeReduction.toFixed(3)}% less downtime`,
        businessImpact: `${savedHours.toFixed(1)} hours/year availability gained`,
        estimatedValue: `$${savedRevenue.toLocaleString()} protected revenue`,
        userExperience: improvedUptime > 99.9 ? 'Enterprise-grade reliability' : 'Improved reliability',
      };
    },
  },
};

/**
 * Performance Patterns
 * Common performance issues and solutions
 */
const PERFORMANCE_PATTERNS = {
  slow_checkout: {
    symptoms: ['High cart abandonment', 'Slow payment page', 'Multiple API calls'],
    metrics: ['Page load time > 3s', 'Time to interactive > 5s', 'API latency > 500ms'],
    solutions: [
      {
        name: 'Edge Computing for APIs',
        impact: '60-80% API latency reduction',
        implementation: 'Move business logic to Akamai edge',
      },
      {
        name: 'Predictive Prefetch',
        impact: '40% faster perceived load',
        implementation: 'Prefetch payment resources during browsing',
      },
      {
        name: 'Progressive Enhancement',
        impact: 'Instant interactivity',
        implementation: 'Show UI immediately, load data async',
      },
    ],
  },
  poor_mobile: {
    symptoms: ['High mobile bounce rate', 'Slow 3G/4G performance', 'Large page sizes'],
    metrics: ['Mobile score < 50', 'Page size > 2MB', 'Requests > 100'],
    solutions: [
      {
        name: 'Adaptive Image Delivery',
        impact: '70% smaller images',
        implementation: 'Automatic format and quality optimization',
      },
      {
        name: 'Mobile-First Caching',
        impact: '90% cache hit rate',
        implementation: 'Aggressive caching for mobile assets',
      },
      {
        name: 'Resource Prioritization',
        impact: '50% faster first paint',
        implementation: 'Critical CSS/JS first, defer non-essential',
      },
    ],
  },
  global_latency: {
    symptoms: ['Slow international users', 'High origin load', 'Inconsistent performance'],
    metrics: ['Geographic latency > 200ms', 'Origin hit rate > 30%', 'Regional complaints'],
    solutions: [
      {
        name: 'Global Load Balancing',
        impact: '< 50ms latency worldwide',
        implementation: 'Automatic traffic routing to nearest PoP',
      },
      {
        name: 'Tiered Caching',
        impact: '95% offload from origin',
        implementation: 'Multi-layer cache architecture',
      },
      {
        name: 'Regional Optimization',
        impact: 'Localized performance',
        implementation: 'Country-specific optimizations',
      },
    ],
  },
};

/**
 * Analytics Insight Engine
 * Generates actionable insights from performance data
 */
class AnalyticsInsightEngine {
  /**
   * Analyze performance intent and identify optimization opportunities
   */
  analyzeIntent(intent: string, context?: BusinessPerformanceContext): {
    performanceGoal: string;
    affectedMetrics: string[];
    businessDriver: string;
    estimatedImpact: string;
    quickWins: string[];
  } {
    const lowerIntent = intent.toLowerCase();
    
    let performanceGoal = 'general_optimization';
    let affectedMetrics: string[] = [];
    let businessDriver = 'user_experience';
    const quickWins: string[] = [];
    
    // Identify performance goals
    if (lowerIntent.includes('checkout') || lowerIntent.includes('cart') || lowerIntent.includes('payment')) {
      performanceGoal = 'checkout_optimization';
      affectedMetrics = ['Page load time', 'API response time', 'Transaction success rate'];
      businessDriver = 'conversion_rate';
      quickWins.push('Enable checkout page prefetch', 'Optimize payment API calls');
    } else if (lowerIntent.includes('mobile') || lowerIntent.includes('app')) {
      performanceGoal = 'mobile_optimization';
      affectedMetrics = ['Mobile page speed', 'Data usage', 'Battery consumption'];
      businessDriver = 'mobile_engagement';
      quickWins.push('Enable image optimization', 'Implement lazy loading');
    } else if (lowerIntent.includes('cost') || lowerIntent.includes('reduce') || lowerIntent.includes('save')) {
      performanceGoal = 'cost_optimization';
      affectedMetrics = ['Bandwidth usage', 'Origin hits', 'Compute resources'];
      businessDriver = 'operational_efficiency';
      quickWins.push('Increase cache TTLs', 'Enable compression');
    } else if (lowerIntent.includes('global') || lowerIntent.includes('international') || lowerIntent.includes('worldwide')) {
      performanceGoal = 'global_performance';
      affectedMetrics = ['Geographic latency', 'Regional availability', 'CDN coverage'];
      businessDriver = 'market_expansion';
      quickWins.push('Enable geo-distributed caching', 'Implement regional failover');
    }
    
    // Estimate business impact
    const estimatedImpact = this.estimateBusinessImpact(performanceGoal, context);
    
    return {
      performanceGoal,
      affectedMetrics,
      businessDriver,
      estimatedImpact,
      quickWins,
    };
  }
  
  /**
   * Generate performance insights from current data
   */
  generateInsights(
    currentMetrics: any,
    goal: string,
    context?: BusinessPerformanceContext
  ): PerformanceInsight[] {
    const insights: PerformanceInsight[] = [];
    
    // Page speed insights
    if (currentMetrics.pageLoadTime) {
      const loadTime = currentMetrics.pageLoadTime;
      if (loadTime > 3000) {
        insights.push({
          type: 'critical',
          metric: 'Page Load Time',
          finding: `Pages loading in ${(loadTime / 1000).toFixed(1)}s (industry standard: <3s)`,
          impact: 'Every second costs 7% in conversions',
          recommendation: 'Implement edge caching and optimization',
          estimatedImprovement: '50-70% faster',
        });
      }
    }
    
    // Mobile insights
    if (currentMetrics.mobileScore && currentMetrics.mobileScore < 70) {
      insights.push({
        type: 'warning',
        metric: 'Mobile Performance',
        finding: `Mobile score: ${currentMetrics.mobileScore}/100`,
        impact: '60% of your traffic is mobile',
        recommendation: 'Enable mobile-specific optimizations',
        estimatedImprovement: '+30 points achievable',
      });
    }
    
    // Cost insights
    if (currentMetrics.originHitRate && currentMetrics.originHitRate > 20) {
      insights.push({
        type: 'opportunity',
        metric: 'Origin Offload',
        finding: `${currentMetrics.originHitRate}% requests hit origin`,
        impact: 'High origin costs and slower performance',
        recommendation: 'Optimize caching rules',
        estimatedImprovement: '80% cost reduction possible',
      });
    }
    
    // Geographic insights
    if (currentMetrics.geoLatency) {
      const slowRegions = Object.entries(currentMetrics.geoLatency)
        .filter(([_, latency]) => (latency as number) > 200)
        .map(([region]) => region);
      
      if (slowRegions.length > 0) {
        insights.push({
          type: 'warning',
          metric: 'Geographic Performance',
          finding: `Slow performance in: ${slowRegions.join(', ')}`,
          impact: 'Lost opportunity in these markets',
          recommendation: 'Enable regional acceleration',
          estimatedImprovement: '<100ms latency achievable',
        });
      }
    }
    
    return insights;
  }
  
  /**
   * Create optimization roadmap
   */
  createOptimizationRoadmap(
    insights: PerformanceInsight[],
    context?: BusinessPerformanceContext
  ): OptimizationRoadmap {
    const roadmap: OptimizationRoadmap = {
      phases: [],
      estimatedTimeline: '',
      expectedOutcomes: [],
      investmentRequired: '',
    };
    
    // Phase 1: Quick wins (1-2 weeks)
    const quickWins = insights.filter(i => i.type === 'opportunity' || i.estimatedImprovement?.includes('easy'));
    if (quickWins.length > 0) {
      roadmap.phases.push({
        name: 'Quick Wins',
        duration: '1-2 weeks',
        actions: quickWins.map(i => ({
          action: i.recommendation,
          impact: i.estimatedImprovement || 'Immediate improvement',
          effort: 'Low',
        })),
        expectedResult: '20-30% performance gain',
      });
    }
    
    // Phase 2: Core optimizations (2-6 weeks)
    const coreOptimizations = insights.filter(i => i.type === 'critical' || i.type === 'warning');
    if (coreOptimizations.length > 0) {
      roadmap.phases.push({
        name: 'Core Performance',
        duration: '2-6 weeks',
        actions: coreOptimizations.map(i => ({
          action: i.recommendation,
          impact: i.estimatedImprovement || 'Significant improvement',
          effort: 'Medium',
        })),
        expectedResult: '50-70% performance gain',
      });
    }
    
    // Phase 3: Advanced optimization (6-12 weeks)
    roadmap.phases.push({
      name: 'Advanced Optimization',
      duration: '6-12 weeks',
      actions: [
        {
          action: 'Implement edge computing',
          impact: 'Near-zero latency for business logic',
          effort: 'High',
        },
        {
          action: 'Machine learning optimization',
          impact: 'Predictive performance tuning',
          effort: 'High',
        },
      ],
      expectedResult: 'Best-in-class performance',
    });
    
    // Set timeline and outcomes
    roadmap.estimatedTimeline = '3-4 months for full optimization';
    roadmap.expectedOutcomes = [
      'Sub-3 second load times globally',
      '20-40% conversion rate improvement',
      '60-80% infrastructure cost reduction',
      'Industry-leading user experience',
    ];
    
    // Investment estimate
    if (context?.performance_baseline === 'poor') {
      roadmap.investmentRequired = 'Significant - Full performance overhaul recommended';
    } else if (context?.performance_baseline === 'average') {
      roadmap.investmentRequired = 'Moderate - Targeted optimizations needed';
    } else {
      roadmap.investmentRequired = 'Low - Fine-tuning and maintenance';
    }
    
    return roadmap;
  }
  
  private estimateBusinessImpact(goal: string, context?: BusinessPerformanceContext): string {
    const impacts: Record<string, string> = {
      checkout_optimization: '15-30% increase in conversion rate',
      mobile_optimization: '40% reduction in mobile bounce rate',
      cost_optimization: '50-70% reduction in infrastructure costs',
      global_performance: 'Access to new markets with <100ms latency',
      general_optimization: '20-40% overall performance improvement',
    };
    
    return impacts[goal] || '20-30% performance improvement';
  }
}

/**
 * Performance Types
 */
interface PerformanceInsight {
  type: 'critical' | 'warning' | 'opportunity';
  metric: string;
  finding: string;
  impact: string;
  recommendation: string;
  estimatedImprovement?: string;
}

interface OptimizationRoadmap {
  phases: OptimizationPhase[];
  estimatedTimeline: string;
  expectedOutcomes: string[];
  investmentRequired: string;
}

interface OptimizationPhase {
  name: string;
  duration: string;
  actions: OptimizationAction[];
  expectedResult: string;
}

interface OptimizationAction {
  action: string;
  impact: string;
  effort: 'Low' | 'Medium' | 'High';
}

/**
 * Real-time Performance Monitor
 */
class PerformanceMonitor {
  /**
   * Generate real-time performance summary
   */
  generateRealtimeSummary(metrics: any): string {
    let summary = `## Real-time Performance Status\n\n`;
    
    // Health indicator
    const healthScore = this.calculateHealthScore(metrics);
    const healthEmoji = healthScore >= 90 ? '🟢' : healthScore >= 70 ? '🟡' : healthScore >= 50 ? '🟠' : '🔴';
    
    summary += `### Overall Health: ${healthEmoji} ${healthScore}/100\n\n`;
    
    // Key metrics
    summary += `**Current Performance**\n`;
    summary += `- Response Time: ${metrics.responseTime || 'N/A'}ms\n`;
    summary += `- Availability: ${metrics.availability || 'N/A'}%\n`;
    summary += `- Error Rate: ${metrics.errorRate || 'N/A'}%\n`;
    summary += `- Cache Hit Rate: ${metrics.cacheHitRate || 'N/A'}%\n\n`;
    
    // Alerts
    if (metrics.activeAlerts && metrics.activeAlerts.length > 0) {
      summary += `**⚠️ Active Alerts**\n`;
      metrics.activeAlerts.forEach((alert: any) => {
        summary += `- ${alert.severity}: ${alert.message}\n`;
      });
      summary += '\n';
    }
    
    // Trending
    summary += `**Trending (vs last hour)**\n`;
    summary += `- Traffic: ${metrics.trafficTrend || 'stable'}\n`;
    summary += `- Performance: ${metrics.performanceTrend || 'stable'}\n`;
    summary += `- Errors: ${metrics.errorTrend || 'stable'}\n`;
    
    return summary;
  }
  
  private calculateHealthScore(metrics: any): number {
    let score = 100;
    
    // Deduct for poor response time
    if (metrics.responseTime > 1000) {score -= 20;}
    else if (metrics.responseTime > 500) {score -= 10;}
    
    // Deduct for availability issues
    if (metrics.availability < 99.9) {score -= 30;}
    else if (metrics.availability < 99.95) {score -= 10;}
    
    // Deduct for high error rate
    if (metrics.errorRate > 5) {score -= 30;}
    else if (metrics.errorRate > 1) {score -= 15;}
    
    // Deduct for poor cache performance
    if (metrics.cacheHitRate < 80) {score -= 10;}
    else if (metrics.cacheHitRate < 90) {score -= 5;}
    
    return Math.max(0, score);
  }
}

/**
 * Main handler for Performance & Analytics Assistant
 */
export async function handlePerformanceAnalyticsAssistant(args: any) {
  const insightEngine = new AnalyticsInsightEngine();
  const monitor = new PerformanceMonitor();
  
  try {
    // Analyze intent
    const analysis = insightEngine.analyzeIntent(args.intent, args.context);
    
    // Build response
    let response = `# Performance & Analytics Assistant\n\n`;
    response += `I'll help you **${args.intent}**.\n\n`;
    
    // Show current status if real-time requested
    if (args.timeframe === 'real_time') {
      // Mock real-time metrics for demo
      const mockMetrics = {
        responseTime: 245,
        availability: 99.98,
        errorRate: 0.02,
        cacheHitRate: 94.5,
        trafficTrend: 'increasing',
        performanceTrend: 'stable',
      };
      
      response += monitor.generateRealtimeSummary(mockMetrics);
      response += '\n';
    }
    
    // Business impact
    response += `## Business Impact Analysis\n\n`;
    response += `**Goal:** ${analysis.performanceGoal.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n`;
    response += `**Expected Impact:** ${analysis.estimatedImpact}\n`;
    response += `**Key Driver:** ${analysis.businessDriver.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n`;
    
    // Quick wins
    if (analysis.quickWins.length > 0) {
      response += `## 🚀 Quick Wins Available\n\n`;
      response += `I can implement these improvements immediately:\n\n`;
      analysis.quickWins.forEach((win, index) => {
        response += `${index + 1}. **${win}**\n`;
        response += `   - Implementation: 1-2 days\n`;
        response += `   - Impact: Immediate improvement\n\n`;
      });
    }
    
    // Performance insights
    const mockCurrentMetrics = {
      pageLoadTime: 4500,
      mobileScore: 65,
      originHitRate: 35,
      geoLatency: {
        'Europe': 250,
        'Asia': 350,
        'South America': 400,
      },
    };
    
    const insights = insightEngine.generateInsights(mockCurrentMetrics, analysis.performanceGoal, args.context);
    
    if (insights.length > 0) {
      response += `## Performance Insights\n\n`;
      
      // Group by type
      const criticalInsights = insights.filter(i => i.type === 'critical');
      const warningInsights = insights.filter(i => i.type === 'warning');
      const opportunityInsights = insights.filter(i => i.type === 'opportunity');
      
      if (criticalInsights.length > 0) {
        response += `### 🔴 Critical Issues\n\n`;
        criticalInsights.forEach(insight => {
          response += `**${insight.metric}**\n`;
          response += `- Finding: ${insight.finding}\n`;
          response += `- Impact: ${insight.impact}\n`;
          response += `- Action: ${insight.recommendation}\n`;
          response += `- Expected: ${insight.estimatedImprovement}\n\n`;
        });
      }
      
      if (warningInsights.length > 0) {
        response += `### 🟡 Warnings\n\n`;
        warningInsights.forEach(insight => {
          response += `**${insight.metric}**\n`;
          response += `- ${insight.finding}\n`;
          response += `- Recommendation: ${insight.recommendation}\n\n`;
        });
      }
      
      if (opportunityInsights.length > 0) {
        response += `### 💡 Opportunities\n\n`;
        opportunityInsights.forEach(insight => {
          response += `**${insight.metric}**\n`;
          response += `- ${insight.finding}\n`;
          response += `- Potential: ${insight.estimatedImprovement}\n\n`;
        });
      }
    }
    
    // Optimization roadmap
    const roadmap = insightEngine.createOptimizationRoadmap(insights, args.context);
    
    response += `## Optimization Roadmap\n\n`;
    response += `**Timeline:** ${roadmap.estimatedTimeline}\n`;
    response += `**Investment:** ${roadmap.investmentRequired}\n\n`;
    
    roadmap.phases.forEach((phase, index) => {
      response += `### Phase ${index + 1}: ${phase.name}\n`;
      response += `Duration: ${phase.duration}\n\n`;
      response += `**Actions:**\n`;
      phase.actions.forEach(action => {
        response += `- ${action.action}\n`;
        response += `  - Impact: ${action.impact}\n`;
        response += `  - Effort: ${action.effort}\n`;
      });
      response += `\n**Expected Result:** ${phase.expectedResult}\n\n`;
    });
    
    // Expected outcomes
    response += `## Expected Business Outcomes\n\n`;
    roadmap.expectedOutcomes.forEach(outcome => {
      response += `- ${outcome}\n`;
    });
    
    // Specific pattern recommendations
    if (analysis.performanceGoal === 'checkout_optimization' && PERFORMANCE_PATTERNS.slow_checkout) {
      const pattern = PERFORMANCE_PATTERNS.slow_checkout;
      response += `\n## Checkout-Specific Optimizations\n\n`;
      response += `Based on common checkout performance issues:\n\n`;
      pattern.solutions.forEach((solution, index) => {
        response += `**${index + 1}. ${solution.name}**\n`;
        response += `- Impact: ${solution.impact}\n`;
        response += `- How: ${solution.implementation}\n\n`;
      });
    }
    
    // Next steps
    response += `\n## Next Steps\n\n`;
    
    if (!args.context?.critical_pages) {
      response += `To provide more specific optimizations, please tell me:\n\n`;
      response += `1. **What are your most critical pages?**\n`;
      response += `   - Homepage\n`;
      response += `   - Product pages\n`;
      response += `   - Checkout flow\n`;
      response += `   - Search results\n\n`;
      
      response += `2. **What's your current performance baseline?**\n`;
      response += `   - Not measured yet\n`;
      response += `   - Poor (>5s load times)\n`;
      response += `   - Average (3-5s)\n`;
      response += `   - Good (<3s)\n\n`;
      
      response += `3. **What matters most to your business?**\n`;
      response += `   - Conversion rate\n`;
      response += `   - User engagement\n`;
      response += `   - Cost reduction\n`;
      response += `   - Global reach\n`;
    } else {
      response += `1. **Review the performance insights and recommendations**\n`;
      response += `2. **Prioritize based on business impact**\n`;
      response += `3. **Start with quick wins for immediate results**\n`;
      response += `4. **Plan the phased optimization approach**\n`;
      response += `5. **Set up monitoring to track improvements**\n`;
    }
    
    response += `\nPerformance is a journey, not a destination. Let's make your site fly! 🚀\n`;
    
    return {
      content: [{
        type: 'text',
        text: response,
      }],
    };
    
  } catch (error) {
    logger.error('Performance Assistant Error:', error);
    
    return {
      content: [{
        type: 'text',
        text: `I want to help optimize your performance, but I need more details.\n\n` +
              `Please tell me:\n` +
              `1. What performance challenge are you facing?\n` +
              `2. What's the business impact?\n` +
              `3. What's your target improvement?\n\n` +
              `Examples of what I can help with:\n` +
              `- "Speed up our checkout process"\n` +
              `- "Improve mobile app performance"\n` +
              `- "Reduce infrastructure costs by 50%"\n` +
              `- "Make our site fast globally"\n\n` +
              `Let's turn your performance data into business results!`,
      }],
    };
  }
}

/**
 * ROI Calculator
 * Shows business value of performance improvements
 */
export class PerformanceROICalculator {
  /**
   * Calculate ROI for performance improvements
   */
  calculateROI(
    improvements: any,
    businessMetrics: any
  ): {
    summary: string;
    monthlyImpact: number;
    yearlyImpact: number;
    paybackPeriod: string;
  } {
    let monthlyImpact = 0;
    
    // Calculate conversion impact
    if (improvements.pageSpeedMs && businessMetrics.monthlyVisitors) {
      const speedImpact = IMPACT_CALCULATORS.page_speed.businessImpact(
        improvements.currentSpeedMs,
        improvements.improvedSpeedMs,
        businessMetrics.monthlyVisitors
      );
      
      // Assume average order value for calculation
      const avgOrderValue = businessMetrics.avgOrderValue || 100;
      monthlyImpact += parseInt(speedImpact.estimatedValue.match(/\d+/)?.[0] || '0') * avgOrderValue;
    }
    
    // Calculate cost savings
    if (improvements.cacheHitRate) {
      const currentOriginCost = businessMetrics.monthlyOriginCost || 10000;
      const hitRateImprovement = improvements.improvedCacheRate - improvements.currentCacheRate;
      const costSaving = currentOriginCost * (hitRateImprovement / 100);
      monthlyImpact += costSaving;
    }
    
    const yearlyImpact = monthlyImpact * 12;
    const investmentCost = businessMetrics.optimizationCost || 50000;
    const paybackMonths = investmentCost / monthlyImpact;
    
    return {
      summary: `Performance optimization will generate $${monthlyImpact.toLocaleString()}/month in value`,
      monthlyImpact,
      yearlyImpact,
      paybackPeriod: `${Math.ceil(paybackMonths)} months`,
    };
  }
}