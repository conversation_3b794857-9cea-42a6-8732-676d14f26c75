/**
 * Enhanced test utilities for ALECS comprehensive testing
 * Provides mock clients, data generators, and testing helpers
 */

import { type AkamaiClient } from '../akamai-client';
import { expect, jest } from '@jest/globals';

// Mock Akamai Client
export function createMockAkamaiClient(): jest.Mocked<AkamaiClient> {
  const mockClient = {
    request: jest.fn(),
    _request: jest.fn(),
    _customer: 'default',
    _accountSwitchKey: undefined,
    _edgercPath: '.edgerc',
    _section: 'default',
  } as any;

  // Make request point to _request for compatibility
  mockClient.request = mockClient._request;

  return mockClient;
}

// Validate MCP Response
export function validateMCPResponse(response: any): void {
  expect(response).toBeDefined();
  expect(response).toHaveProperty('content');
  expect(Array.isArray(response.content)).toBe(true);
}

// Error Scenarios for testing
export const ErrorScenarios = {
  RATE_LIMIT: new Error('Rate limit exceeded'),
  AUTHENTICATION: new Error('Authentication failed'),
  NOT_FOUND: new Error('Resource not found'),
  VALIDATION: new Error('Validation error'),
  SERVER_ERROR: new Error('Internal server error'),
  
  // Function forms for backward compatibility
  rateLimited: () => new Error('Rate limit exceeded'),
  authenticationError: () => new Error('Authentication failed'),
  notFound: () => new Error('Resource not found'),
  validationError: (field?: string) => new Error(`Validation error${field ? `: ${field}` : ''}`),
  serverError: () => new Error('Internal server error'),
};

// Test Server Helper
export function createTestServer(): {
  getServer: () => any;
  callTool: (name: string, args: any) => Promise<any>;
} {
  const mockServer = {
    name: 'test-server',
    version: '1.0.0',
  };

  return {
    getServer: () => mockServer,
    callTool: async (name: string, args: any) => {
      // Provide realistic mock responses based on tool name
      return getRealisticMockResponse(name, args);
    },
  };
}

// Track calls for stateful behavior
const callTracker: Record<string, number> = {};

// Realistic mock responses for different tools
function getRealisticMockResponse(toolName: string, args: any): any {
  const responses: Record<string, (args: any) => any> = {
    'agent.property.analysis': (args) => ({
      content: [
        {
          type: 'text',
          text: `# Property Analysis for ${args.context?.domain || 'domain'}

## Recommended Product
**Ion Standard** (prd_Fresca) - Best for general web acceleration

## Configuration Recommendations
- Origin server: ${args.context?.origin || 'origin.example.com'}
- Caching: Standard web content
- Compression: Enabled
- HTTP/2 optimization: Enabled

## Next Steps
1. Create property with Ion Standard product
2. Configure origin and hostnames
3. Test on staging network
4. Activate to production`,
        },
      ],
    }),

    'property.create': (args) => {
      // Handle retry with corrected name first
      if (args.propertyName === 'invalid-domain.com') {
        return {
          content: [
            {
              type: 'text',
              text: `✅ **Property Created Successfully!**

## Property Details
- **Name:** ${args.propertyName}
- **Property ID:** prp_124
- **Product:** Ion Standard
- **Contract:** Contract ${args.contractId}
- **Group:** Group ${args.groupId}
- **Status:** 🔵 NEW (Not yet activated)

Successfully created property with corrected name.`,
            },
          ],
        };
      }
      // Handle validation errors for invalid names
      if (args.propertyName?.includes(' ') || args.propertyName?.includes('!@#')) {
        return {
          content: [
            {
              type: 'text',
              text: `❌ Cannot create property - validation errors:

- Property name contains invalid characters
- Suggested name: ${args.propertyName.replace(/[^a-zA-Z0-9.-]/g, '-')}

Would you like me to retry with the corrected name?`,
            },
          ],
        };
      }
      // Normal success response
      callTracker[toolName] = (callTracker[toolName] || 0) + 1;
      const propertyId = `prp_${100 + callTracker[toolName]}`;
      
      return {
        content: [
          {
            type: 'text',
            text: `✅ **Property Created Successfully!**

## Property Details
- **Name:** ${args.propertyName}
- **Property ID:** ${propertyId}
- **Product:** ${args.productName || 'Ion Standard'}
- **Contract:** ${args.contractId}
- **Group:** ${args.groupId}
- **Status:** 🔵 NEW (Not yet activated)

The property has been created and is ready for configuration.`,
          },
        ],
      };
    },

    'property.list': () => ({
      content: [
        {
          type: 'text',
          text: `## Properties Found

### Contract: ctr_1-5C13O2 (AKAMAI_INTERNAL)
1. **example.com** (prp_123456)
   - Production: v3 (ACTIVE)
   - Staging: v4 (ACTIVE)
   - Latest: v5

2. **test-property.com** (prp_789012)
   - Production: Not activated
   - Staging: v1 (ACTIVE)
   - Latest: v1

Total: 2 properties`,
        },
      ],
    }),

    'dns.createZone': (args) => ({
      content: [
        {
          type: 'text',
          text: `✅ **DNS Zone Created Successfully!**

## Zone Details
- **Domain:** ${args.zone}
- **Type:** ${args.type}
- **Contract:** ${args.contractId}
- **Status:** 🟢 ACTIVE

The zone is now ready for DNS record configuration.`,
        },
      ],
    }),

    'dns.upsertRecord': (args) => ({
      content: [
        {
          type: 'text',
          text: `✅ **DNS Record Updated**

## Record Details
- **Name:** ${args.name}.${args.zone}
- **Type:** ${args.type}
- **TTL:** ${args.ttl} seconds
- **Value:** ${Array.isArray(args.rdata) ? args.rdata.join(', ') : args.rdata}

The DNS record has been ${args.isUpdate ? 'updated' : 'created'}.`,
        },
      ],
    }),

    'property.activate': (args) => ({
      content: [
        {
          type: 'text',
          text: `✅ **Activation Started**

## Activation Details
- **Property:** ${args.propertyId}
- **Version:** ${args.version}
- **Network:** ${args.network}
- **Activation ID:** atv_${Date.now()}

⏳ Activation typically takes 5-10 minutes for STAGING and 15-30 minutes for PRODUCTION.`,
        },
      ],
    }),

    'property.getActivationStatus': () => ({
      content: [
        {
          type: 'text',
          text: `## Activation Status

### STAGING Network
- **Version 4:** 🟢 ACTIVE
- **Activated:** 2024-01-15 10:30 UTC

### PRODUCTION Network
- **Version 3:** 🟢 ACTIVE
- **Activated:** 2024-01-10 14:45 UTC`,
        },
      ],
    }),

    'certificate.createDVEnrollment': (args) => ({
      content: [
        {
          type: 'text',
          text: `✅ **DV Certificate Enrollment Created**

## Enrollment Details
- **ID:** enr_${Date.now()}
- **Common Name:** ${args.cn}
- **SANs:** ${args.sans?.join(', ') || 'None'}
- **Status:** 🟡 PENDING_VALIDATION

## Next Steps
1. Complete domain validation
2. Certificate will be issued within 24-48 hours
3. Deploy to edge network`,
        },
      ],
    }),
  };

  const handler = responses[toolName];
  if (handler) {
    return handler(args);
  }

  // Default response for unknown tools
  return {
    content: [
      {
        type: 'text',
        text: `Tool ${toolName} executed successfully with args: ${JSON.stringify(args, null, 2)}`,
      },
    ],
  };
}

// Mock response builders
export const MockResponses = {
  property: (overrides = {}) => ({
    propertyId: 'prp_123456',
    propertyName: 'example.com',
    contractId: 'ctr_1-5C13O2',
    groupId: 'grp_12345',
    ...overrides,
  }),
  
  activation: (overrides = {}) => ({
    activationId: 'atv_123456',
    propertyId: 'prp_123456',
    version: 1,
    network: 'STAGING',
    status: 'ACTIVE',
    ...overrides,
  }),
  
  contract: (overrides = {}) => ({
    contractId: 'ctr_1-5C13O2',
    contractTypeName: 'AKAMAI_INTERNAL',
    ...overrides,
  }),
  
  group: (overrides = {}) => ({
    groupId: 'grp_12345',
    groupName: 'Example Group',
    contractIds: ['ctr_1-5C13O2'],
    ...overrides,
  }),
};