version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 10
    groups:
      typescript-eslint:
        patterns:
          - "@typescript-eslint/*"
        update-types:
          - "minor"
          - "patch"
          - "major"
      testing-tools:
        patterns:
          - "jest*"
          - "@jest/*"
          - "fast-check"
          - "*test*"
      build-tools:
        patterns:
          - "typescript"
          - "tsx"
          - "ts-*"
          - "eslint*"
          - "prettier"
      mcp-sdk:
        patterns:
          - "@modelcontextprotocol/*"
    labels:
      - "dependencies"
      - "automated"
    assignees:
      - "acedergren"
    reviewers:
      - "acedergren"