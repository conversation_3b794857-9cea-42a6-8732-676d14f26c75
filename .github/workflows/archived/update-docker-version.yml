name: Update Docker Version

on:
  push:
    branches:
      - main
      - master
      - 'release/**'
    paths:
      - 'package.json'
      - 'docs/changelog/CHANGELOG.md'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to set (optional, will use package.json if not provided)'
        required: false

jobs:
  update-docker-files:
    name: Update Docker Files with Latest Version
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Get version from package.json
        id: get-version
        run: |
          if [ -n "${{ github.event.inputs.version }}" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION=$(node -p "require('./package.json').version")
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Version: $VERSION"

      - name: Update Dockerfile versions
        run: |
          VERSION="${{ steps.get-version.outputs.version }}"

          # Update main Dockerfile
          if [ -f "build/docker/Dockerfile" ]; then
            sed -i "s/org\.opencontainers\.image\.version=\"[^\"]*\"/org.opencontainers.image.version=\"$VERSION\"/g" build/docker/Dockerfile
            echo "Updated build/docker/Dockerfile with version $VERSION"
          fi

          # Update dev Dockerfile if exists
          if [ -f "build/docker/Dockerfile.dev" ]; then
            sed -i "s/org\.opencontainers\.image\.version=\"[^\"]*\"/org.opencontainers.image.version=\"$VERSION\"/g" build/docker/Dockerfile.dev
            echo "Updated build/docker/Dockerfile.dev with version $VERSION"
          fi

          # Update any other Dockerfiles
          find . -name "Dockerfile*" -type f ! -path "./node_modules/*" ! -path "./.git/*" | while read -r dockerfile; do
            if grep -q "org.opencontainers.image.version" "$dockerfile"; then
              sed -i "s/org\.opencontainers\.image\.version=\"[^\"]*\"/org.opencontainers.image.version=\"$VERSION\"/g" "$dockerfile"
              echo "Updated $dockerfile with version $VERSION"
            fi
          done

      - name: Update docker-compose.yml image tags
        run: |
          VERSION="${{ steps.get-version.outputs.version }}"

          # Update docker-compose files to use versioned images
          find . -name "docker-compose*.yml" -type f ! -path "./node_modules/*" ! -path "./.git/*" | while read -r compose_file; do
            # Update image tags to include version
            sed -i "s/alecs-mcp-server-akamai:latest/alecs-mcp-server-akamai:$VERSION/g" "$compose_file"
            sed -i "s/alecs-mcp-server-akamai:dev/alecs-mcp-server-akamai:dev-$VERSION/g" "$compose_file"
            echo "Updated $compose_file with version $VERSION"
          done

      - name: Check for changes
        id: check-changes
        run: |
          if git diff --quiet; then
            echo "changed=false" >> $GITHUB_OUTPUT
            echo "No changes detected"
          else
            echo "changed=true" >> $GITHUB_OUTPUT
            echo "Changes detected:"
            git diff --name-only
          fi

      - name: Create Pull Request
        if: steps.check-changes.outputs.changed == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update Docker files to version ${{ steps.get-version.outputs.version }}'
          title: 'chore: update Docker files to version ${{ steps.get-version.outputs.version }}'
          body: |
            ## 🐳 Docker Version Update

            This PR automatically updates the Docker files with version `${{ steps.get-version.outputs.version }}`.

            ### Changes made:
            - Updated `LABEL org.opencontainers.image.version` in Dockerfiles
            - Updated image tags in docker-compose files

            ### Files modified:
            ```
            ${{ steps.check-changes.outputs.files }}
            ```

            ---
            *This PR was automatically generated by the update-docker-version workflow.*
          branch: update-docker-version-${{ steps.get-version.outputs.version }}
          delete-branch: true
          labels: |
            docker
            version-update
            automated

      - name: Auto-merge PR (optional)
        if: steps.check-changes.outputs.changed == 'true'
        run: |
          # Note: This requires additional permissions and GitHub Apps setup
          # Uncomment and configure if you want auto-merge functionality
          # gh pr merge --auto --merge
