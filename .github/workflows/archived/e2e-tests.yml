name: E2E Tests

on:
  push:
    branches: [main, feat/remote-access-security]
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run E2E tests
        env:
          AKAMAI_CLIENT_SECRET: ${{ secrets.AKAMAI_CLIENT_SECRET }}
          AKAMAI_HOST: ${{ secrets.AKAMAI_HOST }}
          AKAMAI_ACCESS_TOKEN: ${{ secrets.AKAMAI_ACCESS_TOKEN }}
          AKAMAI_CLIENT_TOKEN: ${{ secrets.AKAMAI_CLIENT_TOKEN }}
          NODE_ENV: test
        run: npm run test:e2e:full

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-results
          path: |
            test-results/e2e/
            coverage/
          retention-days: 30

      - name: Comment PR with results (if PR)
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const reportPath = 'test-results/e2e/e2e-test-report.json';

            if (fs.existsSync(reportPath)) {
              const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
              const { totalTests, passed, failed, duration } = report;
              const successRate = totalTests > 0 ? (passed / totalTests * 100).toFixed(1) : '0';
              
              const status = failed === 0 ? '✅' : '❌';
              const emoji = failed === 0 ? '🎉' : '⚠️';
              
              const comment = `## ${status} E2E Test Results
              
              ${emoji} **${passed}/${totalTests}** tests passed (${successRate}% success rate)
              ⏱️ Duration: ${(duration / 1000).toFixed(2)}s
              
              ### Suite Breakdown
              ${report.suites.map(suite => 
                `- ${suite.failed === 0 ? '✅' : '❌'} **${suite.name}**: ${suite.passed}/${suite.tests} passed`
              ).join('\n')}
              
              ${failed > 0 ? '### Action Required\nPlease check the failed tests in the workflow logs.' : '### All tests passed! 🚀'}
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            }

  e2e-workflow-assistants:
    name: Workflow Assistants Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run Workflow Assistant tests
        env:
          AKAMAI_CLIENT_SECRET: ${{ secrets.AKAMAI_CLIENT_SECRET }}
          AKAMAI_HOST: ${{ secrets.AKAMAI_HOST }}
          AKAMAI_ACCESS_TOKEN: ${{ secrets.AKAMAI_ACCESS_TOKEN }}
          AKAMAI_CLIENT_TOKEN: ${{ secrets.AKAMAI_CLIENT_TOKEN }}
          NODE_ENV: test
        run: npm run test:e2e:workflow

      - name: Upload results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: workflow-assistant-test-results
          path: coverage/
          retention-days: 7

  e2e-workflows:
    name: Workflow Orchestration Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Run Workflow tests
        env:
          AKAMAI_CLIENT_SECRET: ${{ secrets.AKAMAI_CLIENT_SECRET }}
          AKAMAI_HOST: ${{ secrets.AKAMAI_HOST }}
          AKAMAI_ACCESS_TOKEN: ${{ secrets.AKAMAI_ACCESS_TOKEN }}
          AKAMAI_CLIENT_TOKEN: ${{ secrets.AKAMAI_CLIENT_TOKEN }}
          NODE_ENV: test
        run: npm run test:e2e:orchestration

      - name: Upload results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: workflow-test-results
          path: coverage/
          retention-days: 7
