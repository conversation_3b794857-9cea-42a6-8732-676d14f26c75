name: Authentication Integration Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    # Run daily at 02:00 UTC to catch credential issues early
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_environment:
        description: 'Test environment (staging or production)'
        required: false
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  auth-test:
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.event.inputs.test_environment || 'staging' }}

    permissions:
      contents: read
      id-token: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Configure 1Password Service Account
        uses: 1password/load-secrets-action@v2
        with:
          export-env: false
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}

      - name: Load Akamai EdgeRC credentials from 1Password
        uses: 1password/load-secrets-action@v2
        with:
          export-env: true
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}
          # Load EdgeRC credentials for different environments
          AKAMAI_CLIENT_TOKEN: op://akamai-credentials/${{ github.event.inputs.test_environment || 'staging' }}/client_token
          AKAMAI_CLIENT_SECRET: op://akamai-credentials/${{ github.event.inputs.test_environment || 'staging' }}/client_secret
          AKAMAI_ACCESS_TOKEN: op://akamai-credentials/${{ github.event.inputs.test_environment || 'staging' }}/access_token
          AKAMAI_HOST: op://akamai-credentials/${{ github.event.inputs.test_environment || 'staging' }}/host
          AKAMAI_ACCOUNT_SWITCH_KEY: op://akamai-credentials/${{ github.event.inputs.test_environment || 'staging' }}/account_switch_key
          # OAuth credentials for testing OAuth integration
          OAUTH_CLIENT_ID: op://oauth-credentials/${{ github.event.inputs.test_environment || 'staging' }}/client_id
          OAUTH_CLIENT_SECRET: op://oauth-credentials/${{ github.event.inputs.test_environment || 'staging' }}/client_secret
          OAUTH_INTROSPECTION_ENDPOINT: op://oauth-credentials/${{ github.event.inputs.test_environment || 'staging' }}/introspection_endpoint

      - name: Create .edgerc file from 1Password secrets
        run: |
          mkdir -p ~/.akamai
          cat > ~/.edgerc << EOF
          [default]
          client_token = $AKAMAI_CLIENT_TOKEN
          client_secret = $AKAMAI_CLIENT_SECRET
          access_token = $AKAMAI_ACCESS_TOKEN
          host = $AKAMAI_HOST
          account-switch-key = $AKAMAI_ACCOUNT_SWITCH_KEY

          [${{ github.event.inputs.test_environment || 'staging' }}]
          client_token = $AKAMAI_CLIENT_TOKEN
          client_secret = $AKAMAI_CLIENT_SECRET
          access_token = $AKAMAI_ACCESS_TOKEN
          host = $AKAMAI_HOST
          account-switch-key = $AKAMAI_ACCOUNT_SWITCH_KEY
          EOF

          # Set secure permissions
          chmod 600 ~/.edgerc

          # Verify file was created
          echo "✅ .edgerc file created with $(wc -l < ~/.edgerc) lines"

      - name: Build project
        run: npm run build

      - name: Run TypeScript checks
        run: npm run typecheck

      - name: Test EdgeRC authentication
        run: npm test -- __tests__/integration/basic-auth-and-contracts.test.ts --verbose
        env:
          TEST_ENVIRONMENT: ${{ github.event.inputs.test_environment || 'staging' }}
          CI: true

      - name: Test OAuth + EdgeRC integration
        run: |
          npm test -- __tests__/integration/multi-customer-oauth.test.ts --verbose || echo "OAuth integration test skipped (optional)"
        env:
          OAUTH_ENABLED: true
          TEST_ENVIRONMENT: ${{ github.event.inputs.test_environment || 'staging' }}
          CI: true
        continue-on-error: true

      - name: Run comprehensive auth tests
        run: |
          # Run auth-specific tests
          npm test -- --testNamePattern="authentication|auth|edgerc|oauth" --verbose
        env:
          TEST_ENVIRONMENT: ${{ github.event.inputs.test_environment || 'staging' }}
          CI: true

      - name: Test Docker build with auth
        run: |
          # Build Docker image with authentication
          docker build -f build/docker/Dockerfile -t alecs-auth-test .

          # Test Docker container can authenticate
          docker run --rm \
            -v ~/.edgerc:/home/<USER>/.edgerc:ro \
            -e NODE_ENV=production \
            alecs-auth-test node -e "
              const { CustomerConfigManager } = require('./dist/utils/customer-config');
              const manager = CustomerConfigManager.getInstance();
              console.log('✅ Docker auth test:', manager.hasSection('default'));
              process.exit(0);
            "

      - name: Cleanup credentials
        if: always()
        run: |
          # Remove credentials file
          rm -f ~/.edgerc
          # Clear environment variables
          unset AKAMAI_CLIENT_TOKEN AKAMAI_CLIENT_SECRET AKAMAI_ACCESS_TOKEN AKAMAI_HOST AKAMAI_ACCOUNT_SWITCH_KEY
          unset OAUTH_CLIENT_ID OAUTH_CLIENT_SECRET OAUTH_INTROSPECTION_ENDPOINT
          echo "✅ Credentials cleaned up"

      - name: Report test results
        if: always()
        run: |
          echo "## Authentication Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ github.event.inputs.test_environment || 'staging' }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build: ${{ job.status }}" >> $GITHUB_STEP_SUMMARY
          echo "- Timestamp: $(date -u)" >> $GITHUB_STEP_SUMMARY

  # Separate job for testing without credentials (validates graceful degradation)
  no-auth-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Test graceful auth failure handling
        run: |
          # Test that the system handles missing credentials gracefully
          npm test -- __tests__/integration/basic-auth-and-contracts.test.ts --verbose
        env:
          CI: true
          NO_AUTH_TEST: true

      - name: Verify error handling
        run: |
          echo "✅ System handles missing credentials gracefully"
