name: Tag Release

on:
  push:
    branches: [main]
    paths:
      - 'package.json'

jobs:
  tag:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      
      - name: Check if version changed
        id: version
        run: |
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          git checkout HEAD~1
          PREVIOUS_VERSION=$(node -p "require('./package.json').version" 2>/dev/null || echo "0.0.0")
          
          if [ "$CURRENT_VERSION" != "$PREVIOUS_VERSION" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            echo "version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
            echo "Version changed from $PREVIOUS_VERSION to $CURRENT_VERSION"
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
      
      - name: Create tag
        if: steps.version.outputs.changed == 'true'
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git tag -a "v${{ steps.version.outputs.version }}" -m "Release v${{ steps.version.outputs.version }}"
          git push origin "v${{ steps.version.outputs.version }}"
      
      - name: Create Release
        if: steps.version.outputs.changed == 'true'
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.version.outputs.version }}
          name: v${{ steps.version.outputs.version }}
          body: |
            Release v${{ steps.version.outputs.version }}
            
            See [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md) for details.
          draft: false
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}