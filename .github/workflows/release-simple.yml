name: Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version bump type'
        required: true
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Bump version
        run: |
          npm version ${{ github.event.inputs.version }} -m "chore: release %s"
          echo "VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_ENV

      - name: Push changes
        run: |
          git push
          git push --tags

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ env.VERSION }}
          name: v${{ env.VERSION }}
          body: |
            Release v${{ env.VERSION }}

            See [commits](https://github.com/${{ github.repository }}/compare/v${{ env.PREVIOUS_VERSION }}...v${{ env.VERSION }}) for changes.
          draft: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Main Docker image (PM2 all-in-one)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:latest
            ghcr.io/${{ github.repository }}:${{ env.VERSION }}

      - name: Build and push Full Docker image (180+ tools)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile.full
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:full-latest
            ghcr.io/${{ github.repository }}:full-${{ env.VERSION }}

      - name: Build and push Essential Docker image (15 tools)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile.essential
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:essential-latest
            ghcr.io/${{ github.repository }}:essential-${{ env.VERSION }}

      - name: Build and push Modular Docker image (domain-specific servers)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile.modular
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:modular-latest
            ghcr.io/${{ github.repository }}:modular-${{ env.VERSION }}

      - name: Build and push Minimal Docker image (3 tools)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile.minimal
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:minimal-latest
            ghcr.io/${{ github.repository }}:minimal-${{ env.VERSION }}

      - name: Build and push Remote Docker image (WebSocket + SSE)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./build/docker/Dockerfile.remote
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:remote-latest
            ghcr.io/${{ github.repository }}:remote-${{ env.VERSION }}
