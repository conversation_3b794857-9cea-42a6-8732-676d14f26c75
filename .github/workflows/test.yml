name: Test

on:
  push:
    branches: [main, develop]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Run CI Diagnostic
        run: node scripts/ci-diagnostic.js

      - name: Lint Check (non-blocking)
        run: npm run lint || true
        continue-on-error: true

      - name: TypeScript Check (non-blocking)
        run: npm run typecheck || true
        continue-on-error: true

      - name: Run Tests (non-blocking)
        run: npm test || true
        continue-on-error: true
