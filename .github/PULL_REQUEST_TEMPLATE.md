## Description
Brief description of the changes in this PR.

## Related Issue
Closes #(issue number)

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Akamai Integration Changes
- [ ] No changes to Akamai API integration
- [ ] Updated existing Akamai API calls
- [ ] Added new Akamai API integration
- [ ] Modified EdgeRC credential handling

If Akamai integration changed, please specify:
- **APIs affected**: 
- **New permissions required**: 
- **Rate limiting considerations**: 

## Testing Checklist
- [ ] Unit tests pass (`npm test`)
- [ ] Integration tests pass
- [ ] Tested with real Akamai credentials
- [ ] Tested multi-customer scenarios
- [ ] No credentials or sensitive data in code
- [ ] Error handling for API failures

## Code Quality
- [ ] Code follows TypeScript style guidelines
- [ ] Self-documenting code with clear naming
- [ ] JSDoc comments for public APIs
- [ ] No ESLint warnings (`npm run lint`)
- [ ] Type checking passes (`npm run typecheck`)

## Documentation
- [ ] README updated (if needed)
- [ ] Wiki documentation updated
- [ ] CHANGELOG.md updated
- [ ] Tool descriptions are clear
- [ ] Example usage provided

## Performance Impact
- [ ] No significant performance impact
- [ ] Performance improved
- [ ] Performance degraded (explain why acceptable)

## Security Checklist
- [ ] No hardcoded credentials
- [ ] Input validation added
- [ ] No sensitive data in logs
- [ ] Rate limiting considered
- [ ] Error messages don't leak information

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Additional Notes
Any additional information that reviewers should know.