---
name: Security Issue
about: Report a security vulnerability (use responsibly)
title: '[SECURITY] '
labels: 'security'
assignees: ''
---

⚠️ **IMPORTANT**: For critical security vulnerabilities, <NAME_EMAIL> instead of creating a public issue.

## Security Issue Type
- [ ] Authentication bypass
- [ ] Token/credential exposure
- [ ] API key leakage
- [ ] Injection vulnerability
- [ ] Configuration weakness
- [ ] Other (please specify)

## Affected Components
- [ ] EdgeRC credential handling
- [ ] Multi-customer authentication
- [ ] Token management
- [ ] API request handling
- [ ] Docker container
- [ ] Other: ___________

## Description
A clear description of the security issue.

## Impact Assessment
- **Severity**: [Critical/High/Medium/Low]
- **Affected Versions**: [e.g., <= 1.0.0]
- **Attack Vector**: [Network/Local/Physical]
- **Privileges Required**: [None/Low/High]

## Steps to Reproduce
1. Configure ALECS with...
2. Send request to...
3. Observe that...

## Proof of Concept
```bash
# Minimal example demonstrating the issue
# Please be responsible and redact sensitive data
```

## Mitigation
Suggested fixes or workarounds:

## References
- CVE ID (if applicable):
- Related issues:
- External references:

## Disclosure Timeline
- Issue discovered:
- Issue reported:
- Response received:
- Fix available:

## Additional Notes
Any other relevant information about the security issue.