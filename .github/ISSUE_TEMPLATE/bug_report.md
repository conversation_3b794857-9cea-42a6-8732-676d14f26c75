---
name: Bug Report
about: Create a report to help us improve ALECS
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## Environment
- **ALECS Version**: [e.g., 1.0.0]
- **Node.js Version**: [e.g., 20.11.0]
- **Operating System**: [e.g., Ubuntu 22.04]
- **Akamai Account Type**: [e.g., Enterprise, Partner]
- **EdgeRC Section Used**: [e.g., default, production]

## Steps to Reproduce
1. Configure ALECS with '...'
2. Execute MCP tool '...'
3. Observe error '...'

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
What actually happened, including any error messages.

## Error Logs
```
Paste any relevant error logs here
```

## Akamai API Details (if applicable)
- **API Endpoint**: [e.g., /papi/v1/properties]
- **Contract ID**: [e.g., ctr_C-1234567]
- **Group ID**: [e.g., grp_12345]
- **Error Code**: [e.g., 401, 403, 500]

## Additional Context
Add any other context about the problem here, such as:
- Recent configuration changes
- Network conditions
- Rate limiting issues

## Possible Solution
If you have suggestions on how to fix the bug, please describe them here.