version: '3.8'

services:
  alecs-essential:
    build:
      context: ../..
      dockerfile: build/docker/Dockerfile.essential
    container_name: alecs-mcp-essential
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=production
      - EDGERC_PATH=/app/.edgerc
    volumes:
      - ~/.edgerc:/app/.edgerc:ro
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'wget', '--spider', '-q', 'http://localhost:3001/health']
      interval: 30s
      timeout: 10s
      retries: 3
