# Multi-stage build for Modular servers
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine

# Install PM2 for running multiple modular servers
RUN npm install -g pm2

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --production && npm cache clean --force

# Copy modular server files from builder
COPY --from=builder /app/dist/servers ./dist/servers
COPY --from=builder /app/dist/tools ./dist/tools
COPY --from=builder /app/dist/utils ./dist/utils
COPY --from=builder /app/dist/types ./dist/types
COPY --from=builder /app/dist/auth ./dist/auth
COPY --from=builder /app/dist/middleware ./dist/middleware
COPY --from=builder /app/dist/core ./dist/core
COPY --from=builder /app/dist/services ./dist/services

# Copy PM2 config for modular servers
COPY ecosystem.config.js ./

# Create directories
RUN mkdir -p data logs

# Use non-root user
USER node

# Expose modular server ports
EXPOSE 3010 3011 3012

# Health check for property server (main modular server)
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s \
  CMD node -e "require('http').get('http://localhost:3010/health', (r) => process.exit(r.statusCode === 200 ? 0 : 1))" || exit 1

# Run only the modular servers (property, dns, security)
CMD ["pm2-runtime", "start", "ecosystem.config.js", "--only", "mcp-akamai-property,mcp-akamai-dns,mcp-akamai-security"]