# Multi-stage build for Essential server
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install production dependencies only
RUN npm ci --production && npm cache clean --force

# Copy only essential server files from builder
COPY --from=builder /app/dist/index-essential.js ./dist/
COPY --from=builder /app/dist/servers/property-server*.js ./dist/servers/
COPY --from=builder /app/dist/servers/dns-server*.js ./dist/servers/
COPY --from=builder /app/dist/tools ./dist/tools
COPY --from=builder /app/dist/utils ./dist/utils
COPY --from=builder /app/dist/types ./dist/types
COPY --from=builder /app/dist/auth ./dist/auth
COPY --from=builder /app/dist/middleware ./dist/middleware
COPY --from=builder /app/dist/core ./dist/core
COPY --from=builder /app/dist/services ./dist/services

# Create directories
RUN mkdir -p data logs

# Use non-root user
USER node

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s \
  CMD node -e "require('http').get('http://localhost:3001/health', (r) => process.exit(r.statusCode === 200 ? 0 : 1))"

CMD ["node", "dist/index-essential.js"]