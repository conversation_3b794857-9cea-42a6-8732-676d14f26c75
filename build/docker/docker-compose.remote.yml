version: '3.8'

services:
  alecs-remote:
    build:
      context: ../..
      dockerfile: build/docker/Dockerfile.remote
    container_name: alecs-remote
    ports:
      - '8080:8080'
    environment:
      - NODE_ENV=production
      - TOKEN_MASTER_KEY=${TOKEN_MASTER_KEY:-change-me-in-production}
      - ALECS_UNIFIED_REMOTE=true
      - ALECS_REMOTE_PORT=8080
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'wget', '--spider', '-q', 'http://localhost:8080/health']
      interval: 30s
      timeout: 10s
      retries: 3
