{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "experimentalDecorators": true, "emitDecoratorMetadata": true, "module": "commonjs", "rootDir": "./src", "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@agents/*": ["src/agents/*"], "@services/*": ["src/services/*"], "@types/*": ["src/types/*"], "@utils/*": ["src/utils/*"], "@config/*": ["src/config/*"], "@constants/*": ["src/constants/*"], "@middleware/*": ["src/middleware/*"]}, "resolveJsonModule": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "allowUnusedLabels": true, "allowUnreachableCode": true, "skipDefaultLibCheck": true, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "__tests__/**/*", "**/*.test.ts", "**/*.spec.ts", "coverage/**/*", "examples/**/*", "docs/**/*"], "ts-node": {"require": ["tsconfig-paths/register"], "transpileOnly": true, "files": true, "compilerOptions": {"module": "commonjs"}}}