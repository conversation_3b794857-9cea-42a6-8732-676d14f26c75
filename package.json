{"name": "alecs-mcp-server-akamai", "version": "1.5.1", "description": "ALECS - A Launchgrid for Edge & Cloud services - MCP server for Akamai with agentic AI workflows", "keywords": ["mcp", "<PERSON><PERSON><PERSON>", "cdn", "dns", "edge-dns", "claude", "ai", "infrastructure", "edgegrid", "property-manager", "alecs"], "homepage": "https://github.com/acedergren/alecs-mcp-server-akamai#readme", "bugs": {"url": "https://github.com/acedergren/alecs-mcp-server-akamai/issues"}, "repository": {"type": "git", "url": "git+https://github.com/acedergren/alecs-mcp-server-akamai.git"}, "main": "dist/index.js", "bin": {"alecs": "dist/index.js"}, "scripts": {"build": "npm run clean && npm run build:ts", "build:dev": "npm run clean && tsc --project tsconfig.json --noEmitOnError false", "build:ts": "tsc --project tsconfig.json", "build:strict": "tsc --project tsconfig.build.json", "build:watch": "tsc --watch --project tsconfig.json", "build:check": "tsc --noEmit --project tsconfig.json", "clean": "rm -rf dist .tsbuildinfo", "prebuild": "echo 'Skipping type check for development build'", "start": "node dist/interactive-launcher.js", "start:interactive": "node dist/interactive-launcher.js", "start:essentials": "node dist/index-essential.js", "start:full": "node dist/index-full.js", "start:property": "node dist/servers/property-server.js", "start:dns": "node dist/servers/dns-server.js", "start:certs": "node dist/servers/certs-server.js", "start:reporting": "node dist/servers/reporting-server.js", "start:security": "node dist/servers/security-server.js", "start:network-lists": "node dist/servers/network-lists-server.js", "start:performance": "node dist/servers/performance-server.js", "start:appsec": "node dist/servers/appsec-server.js", "start:fastpurge": "node dist/servers/fastpurge-server.js", "start:websocket": "node dist/index-websocket.js", "start:websocket:summary": "./scripts/start-websocket-with-summary.sh", "deploy:websocket:summary": "./scripts/start-websocket-with-summary.sh", "start:sse": "node dist/index-sse.js", "start:remote": "node dist/index-remote.js", "deploy:remote": "pm2 start ecosystem.config.js --only alecs-remote", "dev": "tsx src/index.ts", "dev:full": "tsx src/index-full.ts", "dev:essentials": "tsx src/index-essential.ts", "dev:interactive": "tsx src/interactive-launcher.ts", "prepublishOnly": "npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:modular": "jest src/__tests__/modular-servers", "test:property": "jest src/__tests__/modular-servers/property-server.test.ts", "test:dns": "jest src/__tests__/modular-servers/dns-server.test.ts", "test:essentials": "jest src/__tests__/modular-servers/essentials-server.test.ts", "test:workflows": "jest src/__tests__/modular-servers/workflow-integration.test.ts", "test:e2e": "jest __tests__/e2e --runInBand", "test:e2e:workflow": "jest __tests__/e2e/workflow-assistants-e2e.test.ts --runInBand", "test:e2e:orchestration": "jest __tests__/e2e/workflow-orchestration-e2e.test.ts --runInBand", "test:e2e:mcp": "jest __tests__/e2e/mcp-server-e2e.test.ts --runInBand", "test:e2e:full": "ts-node scripts/run-e2e-tests.ts", "test:e2e:verbose": "VERBOSE_TESTS=true npm run test:e2e:full", "ci:self-updating-tests": "tsx ci/test-suite-runner.ts", "ci:validate-env": "tsx ci/validation/test-environment-validator.ts", "ci:process-results": "tsx ci/processing/result-processor.ts", "ci:alert-failure": "tsx ci/alerts/failure-alerter.ts", "test:discover": "tsx ci/discovery/tool-discovery.ts", "test:generate": "tsx ci/generation/test-generator.ts", "test:update": "tsx ci/updates/test-updater.ts", "dev:alex-tests": "tsx --watch ci/test-suite-runner.ts --mode=development", "lint": "eslint 'src/**/*.ts' --fix", "lint:check": "eslint 'src/**/*.ts'", "format": "prettier --write 'src/**/*.{ts,json,md}'", "format:check": "prettier --check 'src/**/*.{ts,json,md}'", "typecheck": "tsc --noEmit --project tsconfig.build.json", "typecheck:strict": "tsc --noEmit --project tsconfig.build.json", "typecheck:dev": "tsc --noEmit --project tsconfig.json", "typecheck:watch": "tsc --noEmit --watch --project tsconfig.json", "postinstall": "npm run build:check || true", "prepare": "npm run build", "demo:agents": "tsx examples/agent-demo.ts", "cleanup": "tsx src/agents/cleanup-agent.ts", "cleanup:dry": "tsx src/agents/cleanup-agent.ts --dry-run", "cleanup:interactive": "tsx src/agents/cleanup-agent.ts --interactive", "sbom": "./scripts/generate-sbom.sh", "licenses": "npx license-checker --summary", "audit": "npm audit", "test:validate": "node tests/run-comprehensive-validation.js", "test:health": "node tests/diagnostics/mcp-health-check.js", "test:journey": "node tests/workflows/customer-journey.js", "test:errors": "node tests/edge-cases/error-scenarios.js", "test:performance": "node --expose-gc tests/performance/load-testing.js", "security:test": "tsx ci/security/run-security-tests.ts", "security:test:quick": "tsx ci/security/run-security-tests.ts --quick", "security:test:auth": "tsx ci/security/run-security-tests.ts --focus=auth", "security:test:report": "tsx ci/security/run-security-tests.ts --format=markdown", "security:scan": "npm audit --production && npm run security:test", "security:fix": "npm audit fix && npm run security:test"}, "_moduleAliases": {"@utils": "dist/utils", "@services": "dist/services", "@types": "dist/types", "@agents": "dist/agents", "@config": "dist/config", "@constants": "dist/constants", "@middleware": "dist/middleware"}, "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "@types/commander": "^2.12.0", "@types/ioredis": "^4.28.10", "@types/ws": "^8.18.1", "akamai-edgegrid": "^3.4.0", "commander": "^14.0.0", "express": "^4.21.2", "ioredis": "^5.6.1", "lru-cache": "^7.18.3", "module-alias": "^2.2.3", "ws": "^8.18.2", "zod": "^3.22.0"}, "devDependencies": {"@jest/globals": "^30.0.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/lru-cache": "^7.10.9", "@types/node": "^24.0.3", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-jest": "^29.0.1", "fast-check": "^4.1.1", "jest": "^30.0.2", "jest-junit": "^16.0.0", "jsonwebtoken": "^9.0.2", "prettier": "^3.0.0", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.0.0", "typescript-eslint": "^8.34.1"}, "files": ["dist/", "README.md", "EXAMPLES.md"], "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}}