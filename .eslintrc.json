{"root": true, "env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:import/typescript", "plugin:jest/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.eslint.json"}, "plugins": ["@typescript-eslint", "import", "jest"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/explicit-function-return-type": ["error", {"allowExpressions": true, "allowTypedFunctionExpressions": true}], "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/prefer-nullish-coalescing": "warn", "@typescript-eslint/prefer-optional-chain": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error"}, "overrides": [{"files": ["**/*.test.ts", "**/*.spec.ts"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "off"}}], "ignorePatterns": ["dist/**", "node_modules/**", "coverage/**", "*.js", "*.d.ts", "examples/**"]}