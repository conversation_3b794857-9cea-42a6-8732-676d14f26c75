version: '3.8'

services:
  alecs:
    build:
      context: .
      dockerfile: build/docker/Dockerfile
    container_name: alecs-mcp-server
    ports:
      - '3000:3000' # Main server
      - '3013:3013' # SSE server
      - '8082:8082' # WebSocket server
    environment:
      - NODE_ENV=production
      - TOKEN_MASTER_KEY=${TOKEN_MASTER_KEY:-development-key}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/health']
      interval: 30s
      timeout: 10s
      retries: 3
