# ALECS MCP Server - Environment Configuration
# ============================================
# Copy this file to .env and update with your settings
# All variables are optional with sensible defaults

# Server Environment
# Options: development, production, test
NODE_ENV=production

# Logging Configuration
# Options: error, warn, info, debug
ALECS_LOG_LEVEL=info

# Pretty print logs in development (default: true in dev, false in prod)
# ALECS_LOG_PRETTY=false

# === WebSocket Server Configuration ===
# Only needed if running WebSocket server mode

# WebSocket server port (default: 8082)
ALECS_WS_PORT=8082

# WebSocket server host (default: 0.0.0.0)
# Use 127.0.0.1 to restrict to localhost only
ALECS_WS_HOST=0.0.0.0

# WebSocket path (default: /mcp)
ALECS_WS_PATH=/mcp

# Authentication token for WebSocket connections
# Generate a secure token: openssl rand -base64 32
TOKEN_MASTER_KEY=your-secret-key-here

# SSL/TLS Configuration (optional)
# Provide paths to certificate files for HTTPS/WSS
# ALECS_SSL_CERT=/path/to/cert.pem
# ALECS_SSL_KEY=/path/to/key.pem

# === Multi-Customer Configuration ===
# For supporting multiple Akamai customers/accounts

# Default customer identifier (matches .edgerc section)
# ALECS_DEFAULT_CUSTOMER=default

# Default EdgeGrid section to use from .edgerc
# ALECS_DEFAULT_SECTION=default

# === Caching Configuration ===
# Redis/Valkey connection for caching (optional)
# REDIS_URL=redis://localhost:6379

# Cache TTL in seconds (default: 300)
# CACHE_TTL=300

# === Observability Configuration ===
# OpenTelemetry configuration (optional)

# OTLP endpoint for traces and metrics
# OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318

# Service name for telemetry
# OTEL_SERVICE_NAME=alecs-mcp-server

# Additional OTEL configuration
# OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://localhost:4318/v1/traces
# OTEL_EXPORTER_OTLP_METRICS_ENDPOINT=http://localhost:4318/v1/metrics

# === API Rate Limiting ===
# Configure rate limits for Akamai API calls

# Max requests per second (default: 10)
# ALECS_RATE_LIMIT=10

# Rate limit window in seconds (default: 1)
# ALECS_RATE_WINDOW=1

# === Development Settings ===
# Settings useful during development

# Skip TypeScript type checking during build
# SKIP_TYPE_CHECK=false

# Enable source maps in production
# ENABLE_SOURCE_MAPS=false

# === Docker-Specific Settings ===
# These are set automatically in Docker but can be overridden

# Health check port (used by Docker health checks)
# HEALTH_CHECK_PORT=8081

# === Feature Flags ===
# Enable/disable specific features

# Enable experimental features
# ENABLE_EXPERIMENTAL=false

# Enable debug endpoints (never enable in production!)
# ENABLE_DEBUG_ENDPOINTS=false

# === Performance Tuning ===
# Advanced performance settings

# Worker threads for parallel operations
# WORKER_THREADS=4

# HTTP timeout in milliseconds
# HTTP_TIMEOUT=30000

# Maximum retry attempts for failed API calls
# MAX_RETRIES=3

# Retry delay in milliseconds
# RETRY_DELAY=1000