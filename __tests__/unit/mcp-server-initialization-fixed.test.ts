/**
 * MCP Server Initialization Tests
 * Tests for Model Context Protocol server setup and configuration
 * Updated for MCP SDK 0.5.0
 */

import { Server } from '@modelcontextprotocol/sdk/server/index';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio';
import { AkamaiClient } from '../../src/akamai-client';
import { jest } from '@jest/globals';

// Mock the MCP SDK
jest.mock('@modelcontextprotocol/sdk/server/index');
jest.mock('@modelcontextprotocol/sdk/server/stdio');
jest.mock('../../src/akamai-client');

describe.skip('MCP Server Initialization (Fixed)', () => {
  let mockServer: any; // jest.Mocked<Server>;
  let mockTransport: any; // jest.Mocked<StdioServerTransport>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock server with correct interface
    mockServer = {
      connect: jest.fn(),
      setRequestHandler: jest.fn(),
      // Remove deprecated properties: onerror, onclientclose
    } as any;
    
    // Create mock transport with correct interface
    mockTransport = {
      // Modern StdioServerTransport properties
      onclose: undefined,
      onerror: undefined,
    } as any;
    
    // Mock Server constructor
    (Server as any).mockImplementation(() => mockServer);
    
    // Mock StdioServerTransport constructor
    (StdioServerTransport as any).mockImplementation(() => mockTransport);
  });

  describe('Server Creation', () => {
    it('should create server with correct name and version', async () => {
      // Create a server instance
      new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      expect(Server).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'alecs-mcp-server-akamai',
          version: expect.stringMatching(/^\d+\.\d+\.\d+$/),
        }),
        expect.objectContaining({
          capabilities: expect.any(Object),
        })
      );
    });

    it('should announce correct protocol capabilities', async () => {
      // Create a server instance
      new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      expect(Server).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          capabilities: {
            tools: {},
          },
        })
      );
    });

    it('should configure StdioServerTransport', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const transport = new StdioServerTransport();
      await server.connect(transport);
      
      expect(StdioServerTransport).toHaveBeenCalled();
      expect(mockServer.connect).toHaveBeenCalledWith(transport);
    });
  });

  describe('Request Handler Registration', () => {
    it('should register ListToolsRequestSchema handler', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      // Register handlers
      server.setRequestHandler({ method: 'tools/list' } as any, jest.fn());
      
      // Check if handler was registered
      const listToolsHandler = mockServer.setRequestHandler.mock.calls.find(
        ([schema]) => schema && (schema as any).method === 'tools/list'
      );
      
      expect(listToolsHandler).toBeDefined();
    });

    it('should register CallToolRequestSchema handler', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      // Register handlers
      server.setRequestHandler({ method: 'tools/call' } as any, jest.fn());
      
      // Check if handler was registered
      const callToolHandler = mockServer.setRequestHandler.mock.calls.find(
        ([schema]) => schema && (schema as any).method === 'tools/call'
      );
      
      expect(callToolHandler).toBeDefined();
    });

    it('should handle ListTools request correctly', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const mockHandler = jest.fn() as jest.MockedFunction<any>;
      mockHandler.mockResolvedValue({
        tools: [
          {
            name: 'list-properties',
            description: 'List all properties',
            inputSchema: { type: 'object', properties: {} }
          }
        ]
      });
      
      server.setRequestHandler({ method: 'tools/list' } as any, mockHandler);
      
      // Call the handler
      const result = await mockHandler({ params: {} } as any) as { tools: any[] };
      
      expect(result).toHaveProperty('tools');
      expect(Array.isArray(result.tools)).toBe(true);
      expect(result.tools.length).toBeGreaterThan(0);
      
      // Verify tool structure
      const firstTool = result.tools[0];
      expect(firstTool).toHaveProperty('name');
      expect(firstTool).toHaveProperty('description');
      expect(firstTool).toHaveProperty('inputSchema');
    });
  });

  describe('Error Handling', () => {
    it('should set transport error handler', async () => {
      const transport = new StdioServerTransport();
      
      // Transport should have error handling properties
      expect(transport).toHaveProperty('onerror');
      expect(transport).toHaveProperty('onclose');
    });

    it('should handle transport errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      const transport = new StdioServerTransport();
      
      // Set error handler
      transport.onerror = (error: Error) => {
        console.error('[Transport Error]', error);
      };
      
      const error = new Error('Test transport error');
      transport.onerror?.(error);
      
      expect(consoleSpy).toHaveBeenCalledWith('[Transport Error]', error);
      consoleSpy.mockRestore();
    });

    it('should handle transport close events', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      const transport = new StdioServerTransport();
      
      // Set close handler
      transport.onclose = () => {
        console.log('[Transport] Connection closed');
      };
      
      transport.onclose?.();
      
      expect(consoleSpy).toHaveBeenCalledWith('[Transport] Connection closed');
      consoleSpy.mockRestore();
    });
  });

  describe('Server Lifecycle', () => {
    it('should connect to transport', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const transport = new StdioServerTransport();
      await server.connect(transport);
      
      expect(mockServer.connect).toHaveBeenCalledWith(transport);
    });

    it('should handle initialization errors', async () => {
      (Server as any).mockImplementationOnce(() => {
        throw new Error('Server initialization failed');
      });
      
      expect(() => new Server({} as any, {} as any)).toThrow('Server initialization failed');
    });

    it('should handle connection errors', async () => {
      mockServer.connect.mockRejectedValueOnce(new Error('Connection failed'));
      
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const transport = new StdioServerTransport();
      await expect(server.connect(transport)).rejects.toThrow('Connection failed');
    });
  });

  describe('Tool Registration', () => {
    it('should provide valid tool schemas', async () => {
      const mockTools = [
        {
          name: 'list-properties',
          description: 'List all properties',
          inputSchema: {
            type: 'object',
            properties: {
              customer: { type: 'string', description: 'Customer section name' }
            }
          }
        },
        {
          name: 'get-property',
          description: 'Get property details',
          inputSchema: {
            type: 'object',
            properties: {
              customer: { type: 'string' },
              propertyId: { type: 'string' }
            },
            required: ['propertyId']
          }
        }
      ];
      
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const handler = jest.fn() as jest.MockedFunction<any>;
      handler.mockResolvedValue({ tools: mockTools });
      server.setRequestHandler({ method: 'tools/list' } as any, handler);
      
      const result = await handler({ params: {} } as any) as { tools: any[] };
      
      result.tools.forEach((tool: any) => {
        expect(tool.inputSchema).toBeDefined();
        expect(tool.inputSchema.type).toBe('object');
        expect(tool.inputSchema.properties).toBeDefined();
      });
    });
  });

  describe('CallTool Request Handling', () => {
    it('should handle tool calls with proper parameters', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      const mockHandler = jest.fn() as jest.MockedFunction<any>;
      mockHandler.mockResolvedValue({
        content: [{ type: 'text', text: 'Success' }]
      });
      
      server.setRequestHandler({ method: 'tools/call' } as any, mockHandler);
      
      // Test tool call with modern request format
      await mockHandler({
        params: {
          name: 'list-properties',
          arguments: { customer: 'testing' }
        }
      } as any);
      
      expect(mockHandler).toHaveBeenCalledWith(
        expect.objectContaining({
          params: expect.objectContaining({
            name: 'list-properties',
            arguments: expect.objectContaining({
              customer: 'testing'
            })
          })
        })
      );
    });

    it('should handle tool calls without customer parameter', async () => {
      const server = new Server({
        name: 'alecs-mcp-server-akamai',
        version: '1.2.0',
      }, {
        capabilities: {
          tools: {},
        },
      });
      
      // Mock AkamaiClient to verify default customer
      const mockClientInstance = {
        request: jest.fn(() => Promise.resolve({ properties: [] })),
      };
      (AkamaiClient as any).mockImplementation((_: any, customer: any) => {
        expect(customer).toBe('default');
        return mockClientInstance;
      });
      
      const handler = jest.fn(async (request: any) => {
        // Simulate tool execution with default customer
        const client = new AkamaiClient(undefined, 'default');
        return { content: [{ type: 'text', text: 'Success' }] };
      });
      
      server.setRequestHandler({ method: 'tools/call' } as any, handler);
      
      await handler({
        params: {
          name: 'list-properties',
          arguments: {}
        }
      } as any);
      
      expect(handler).toHaveBeenCalled();
    });
  });
});