# ALECS MCP Server Documentation

Welcome to the ALECS MCP Server documentation. This guide will help you get started with the Akamai MCP integration.

## 📚 Documentation Structure

### 🚀 [Getting Started](./getting-started/)
- [Installation Guide](./getting-started/installation.md) - Set up your environment
- [Quick Start](./getting-started/quick-start.md) - 5-minute tutorial
- [Configuration](./getting-started/configuration.md) - Configuration options
- [Docker Setup](./getting-started/docker.md) - Container deployment

### 🔧 [API Reference](./api/)
- [Complete API Reference](./api/reference.md) - All available APIs
- [Property Manager APIs](./api/property-manager.md) - CDN property management
- [Edge DNS APIs](./api/edge-dns.md) - DNS zone management
- [FastPurge APIs](./api/fast-purge.md) - Content purging
- [Certificates APIs](./api/certificates.md) - SSL/TLS certificates
- [Reporting APIs](./api/reporting.md) - Analytics and reporting

### 🏗️ [Architecture](./architecture/)
- [System Design](./architecture/system-design.md) - Overall architecture
- [Multi-Customer Support](./architecture/multi-customer.md) - Customer isolation
- [Modular Servers](./architecture/modular-servers.md) - Server components
- [Observability](./architecture/observability.md) - Monitoring and logging

### 🔌 [Integrations](./integrations/)
- [Claude/LLM Integration](./integrations/claude.md) - AI assistant setup
- [CI/CD Integration](./integrations/ci-cd.md) - Automation pipelines
- [LLM Compatibility](./integrations/llm-compatibility.md) - Other AI models

### 🛠️ [Operations](./operations/)
- [CDN Provisioning](./operations/cdn-provisioning.md) - Property workflows
- [Property Onboarding](./operations/property-onboarding.md) - Onboarding guide
- [DNS Migration](./operations/dns-migration.md) - Migration procedures
- [Troubleshooting](./operations/troubleshooting.md) - Common issues

### 💻 [Development](./development/)
- [Development Setup](./development/setup.md) - Local environment
- [Contributing](./development/contributing.md) - Contribution guide
- [Adding Tools](./development/adding-tools.md) - Extend functionality
- [Testing](./development/testing.md) - Test guidelines
- [Workflows](./development/workflows.md) - Development processes

## 🔍 Quick Links

- **First Time?** Start with the [Installation Guide](./getting-started/installation.md)
- **Need API docs?** Check the [API Reference](./api/reference.md)
- **Having issues?** See [Troubleshooting](./operations/troubleshooting.md)
- **Want to contribute?** Read the [Contributing Guide](./development/contributing.md)

## 📖 Additional Resources

- [Project Wiki](./wiki/) - Detailed technical documentation
- [Archive](./archive/) - Historical documents for reference
- [GitHub Repository](https://github.com/your-org/alecs-mcp-server) - Source code
- [Akamai Developer Docs](https://techdocs.akamai.com) - Official Akamai documentation
