{"metadata": {"title": "Akamai Parameter Relationship Documentation", "description": "Complex parameter relationships and dependencies across all Akamai APIs", "version": "1.0.0", "created": "2025-01-18", "last_updated": "2025-01-18"}, "contract_group_dependencies": {"overview": {"description": "Contract and Group relationships form the foundation of Akamai's organizational hierarchy", "hierarchy": "Account > Contract > Group > Property/Zone/Configuration", "inheritance": "Properties inherit contract and group from creation context"}, "contract_requirements": {"required_endpoints": {"property_manager": ["/papi/v1/properties (create)", "/papi/v1/edgehostnames (create)", "/papi/v1/cp-codes (create)", "/papi/v1/includes (create)"], "edge_dns": ["/config-dns/v2/zones (create)"], "certificate_provisioning": ["/cps/v2/enrollments (create)"], "application_security": ["/appsec/v1/configs (create)"]}, "optional_endpoints": {"property_manager": ["/papi/v1/properties (list with filter)", "/papi/v1/groups (list with filter)"], "edge_dns": ["/config-dns/v2/zones (list with filter)"]}, "not_required_endpoints": {"property_manager": ["/papi/v1/properties/{propertyId} (get)", "/papi/v1/properties/{propertyId}/versions", "/papi/v1/properties/{propertyId}/activations"], "edge_dns": ["/config-dns/v2/zones/{zone}/recordsets", "/config-dns/v2/changelists"], "fast_purge": ["All endpoints - operates on existing properties"]}}, "group_requirements": {"required_endpoints": {"property_manager": ["/papi/v1/properties (create)", "/papi/v1/includes (create)"]}, "optional_endpoints": {"property_manager": ["/papi/v1/properties (list with filter)", "/papi/v1/edgehostnames (create)", "/papi/v1/cp-codes (create)"], "edge_dns": ["/config-dns/v2/zones (create)"]}}, "both_required": {"endpoints": ["/papi/v1/properties (create)", "/papi/v1/includes (create)"], "business_rules": ["Property creation requires explicit contract and group assignment", "Include creation requires both for organizational hierarchy", "Contract must contain the specified group", "User must have access to both contract and group"]}, "validation_rules": {"contract_group_relationship": {"rule": "groupId must belong to contractId", "validation_endpoint": "/papi/v1/groups?contractId={contractId}", "error_if_invalid": "Group not found in specified contract"}, "access_permissions": {"rule": "User must have access to both resources", "validation_method": "Implicit via successful API call", "error_codes": ["403 Forbidden", "404 Not Found"]}}}, "property_configuration_dependencies": {"property_id_formats": {"pattern": "^prp_[0-9]+$", "examples": ["prp_123456", "prp_789012"], "validation": "Must be numeric after prp_ prefix", "immutable": true, "generated_by": "Property creation endpoint"}, "version_dependencies": {"latest_version": {"description": "Most recent version of property configuration", "usage": "Default when version not specified", "endpoints_using": ["/papi/v1/properties/{propertyId}/versions/{version}/rules (default)", "/papi/v1/properties/{propertyId}/versions/{version}/hostnames (default)"]}, "production_version": {"description": "Currently active version on production network", "usage": "Read-only for most operations", "can_be_null": true, "modification_via": "Activation process only"}, "staging_version": {"description": "Currently active version on staging network", "usage": "Testing and validation", "can_be_null": true, "modification_via": "Activation process only"}, "version_creation_rules": {"base_version": "New versions created from specified base version", "auto_increment": "Version numbers auto-assigned sequentially", "immutable_after_creation": "Rule trees become read-only after version creation"}}, "rule_tree_dependencies": {"rule_format_version": {"description": "Schema version for rule tree structure", "pattern": "^v\\d{4}-\\d{2}-\\d{2}$", "examples": ["v2023-10-30", "v2023-05-30"], "immutable_per_version": true, "upgrade_required": "For new features and behaviors"}, "behavior_availability": {"description": "Behaviors available depend on rule format version", "validation_endpoint": "/papi/v1/catalog/behaviors", "version_parameter": "ruleFormat", "backward_compatibility": "Older behaviors supported in newer formats"}, "criteria_availability": {"description": "Criteria available depend on rule format version", "validation_endpoint": "/papi/v1/catalog/criteria", "version_parameter": "ruleFormat", "dependency_checking": "Required for rule tree validation"}}, "network_environments": {"staging": {"description": "Testing environment with same configuration as production", "use_cases": ["Development testing", "Configuration validation", "Performance testing"], "activation_requirements": ["Valid property version", "Activation note"], "typical_activation_time": "5-10 minutes"}, "production": {"description": "Live environment serving end-user traffic", "additional_requirements": ["Prior staging validation recommended", "Business approval often required"], "activation_requirements": ["Valid property version", "Activation note", "Notification emails"], "typical_activation_time": "5-15 minutes"}}, "hostname_relationships": {"edge_hostnames": {"description": "Akamai edge hostnames that serve traffic", "pattern": "*.edgesuite.net, *.edgekey.net, *.akamaized.net", "creation_dependency": "Requires SSL certificate configuration", "association": "Linked to properties via hostname objects"}, "property_hostnames": {"description": "Customer hostnames mapped to edge hostnames", "relationship": "Many customer hostnames can map to one edge hostname", "certificate_dependency": "Must match certificate Subject Alternative Names", "dns_dependency": "Customer DNS must CNAME to edge hostname"}, "certificate_integration": {"default_dv": "Automatic certificate provisioning for edge hostnames", "cps_certificates": "Custom certificates require explicit association", "validation_required": "Domain validation must complete before activation"}}}, "dns_edge_configuration_parameters": {"zone_contract_relationships": {"zone_creation": {"contract_required": true, "group_optional": true, "inheritance": "Zone inherits contract permissions and billing", "multi_contract_support": false}, "record_operations": {"contract_inherited": "Records inherit zone's contract association", "no_explicit_contract": "Contract not specified in record operations", "billing_attribution": "Record operations billed to zone's contract"}}, "changelist_workflow": {"description": "DNS changes require changelist workflow for activation", "workflow_steps": ["Create or get existing changelist", "Add/modify/delete records in changelist", "Submit changelist for activation", "Monitor activation status"], "changelist_id": {"generation": "Automatically generated per zone", "persistence": "Single active changelist per zone", "reset_after_activation": "New changelist created after successful activation"}}, "zone_type_dependencies": {"PRIMARY": {"description": "Authoritative zone managed by Akamai", "required_parameters": ["zone", "contractId"], "optional_parameters": ["comment", "signAndServe", "tsig<PERSON><PERSON>"], "record_management": "Full CRUD operations supported"}, "SECONDARY": {"description": "Zone transferred from external master servers", "required_parameters": ["zone", "contractId", "masters"], "optional_parameters": ["tsig<PERSON><PERSON>"], "record_management": "Read-only, managed via zone transfer"}, "ALIAS": {"description": "Zone aliased to another zone", "required_parameters": ["zone", "contractId", "target"], "record_management": "Inherited from target zone"}}, "dnssec_dependencies": {"sign_and_serve": {"description": "Automatic DNSSEC signing by <PERSON><PERSON><PERSON><PERSON>", "prerequisite": "Zone must be PRIMARY type", "algorithm_options": ["RSASHA256", "ECDSAP256SHA256"], "key_management": "Automated by <PERSON><PERSON><PERSON><PERSON>", "ds_record_publication": "Customer responsibility at parent zone"}}}, "certificate_ssl_parameters": {"validation_types": {"dv": {"description": "Domain Validated certificates", "validation_method": "DNS or HTTP challenges", "automation_support": "Full automation with ACME protocol", "certificate_authority": "Let's Encrypt or Sectigo", "validation_time": "5-30 minutes", "required_parameters": ["csr.cn", "adminContact", "techContact", "org"]}, "ov": {"description": "Organization Validated certificates", "validation_method": "Organization verification required", "automation_support": "Limited - manual verification steps", "validation_time": "1-3 business days", "additional_requirements": ["Organization documentation", "Phone verification"]}, "ev": {"description": "Extended Validation certificates", "validation_method": "Enhanced organization verification", "automation_support": "None - full manual process", "validation_time": "3-7 business days", "additional_requirements": ["Legal entity verification", "Physical address verification"]}}, "network_configuration_dependencies": {"geography": {"core": {"description": "Global deployment excluding China and Russia", "certificate_authority_support": ["Let's Encrypt", "Sectigo", "<PERSON><PERSON><PERSON><PERSON>"], "deployment_time": "5-15 minutes"}, "china": {"description": "China-specific deployment", "certificate_authority_restrictions": "Local CA requirements", "additional_compliance": "ICP licensing requirements"}, "russia": {"description": "Russia-specific deployment", "certificate_authority_restrictions": "Local CA requirements", "additional_compliance": "Data localization requirements"}}, "secure_network": {"standard-tls": {"description": "Standard TLS configuration", "tls_versions": ["1.2", "1.3"], "cipher_suites": "Akamai default selection", "certificate_types": ["san", "wildcard", "single"]}, "enhanced-tls": {"description": "Enhanced TLS with additional security features", "additional_features": ["OCSP stapling", "Client certificate authentication"], "performance_optimization": "Optimized cipher selections"}, "shared-cert": {"description": "Shared certificate across multiple customers", "cost_optimization": "Lower cost option", "hostname_limitations": "Limited SAN availability"}}}, "domain_validation_workflows": {"dns_validation": {"challenge_type": "dns-01", "record_requirements": {"type": "TXT", "name": "_acme-challenge.{domain}", "value": "Challenge token provided by CA"}, "automation_support": "Full automation via Edge DNS integration", "validation_time": "5-10 minutes"}, "http_validation": {"challenge_type": "http-01", "path_requirements": "/.well-known/acme-challenge/{token}", "content_requirements": "Key authorization string", "limitations": "Not supported for wildcard certificates"}}, "certificate_deployment": {"edge_hostname_association": {"description": "Certificates must be associated with edge hostnames", "validation_required": "Domain names must match certificate SAN list", "deployment_stages": ["staging", "production"], "activation_dependency": "Property activation propagates certificate deployment"}, "renewal_automation": {"dv_certificates": "Automatic renewal 30 days before expiration", "renewal_validation": "Automated re-validation using stored challenges", "notification_system": "Email alerts for renewal status"}}}, "parameter_validation_rules": {"format_requirements": {"identifiers": {"property_id": {"pattern": "^prp_[0-9]+$", "description": "Property identifier with numeric suffix", "immutable": true, "system_generated": true}, "contract_id": {"pattern": "^ctr_[A-Z0-9-]+$", "description": "Contract identifier with alphanumeric suffix", "immutable": true, "customer_visible": true}, "group_id": {"pattern": "^grp_[0-9]+$", "description": "Group identifier with numeric suffix", "immutable": true, "hierarchy": "Must belong to specified contract"}, "activation_id": {"pattern": "^act_[0-9]+$", "description": "Activation identifier for tracking", "immutable": true, "system_generated": true}}, "network_identifiers": {"hostname": {"pattern": "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?)*$", "max_length": 255, "labels_max_length": 63, "case_insensitive": true}, "ip_address": {"ipv4_pattern": "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", "ipv6_pattern": "^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::$", "cidr_pattern": "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\/(?:[0-9]|[1-2][0-9]|3[0-2])$"}}, "time_formats": {"iso8601": {"pattern": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d{3})?Z$", "description": "UTC timestamp in ISO 8601 format", "examples": ["2025-01-18T10:30:00Z", "2025-01-18T10:30:00.123Z"]}, "rule_format_version": {"pattern": "^v\\d{4}-\\d{2}-\\d{2}$", "description": "Property rule format version", "examples": ["v2023-10-30", "v2023-05-30"]}}}, "mutually_exclusive_parameters": {"property_creation": {"product_template_vs_custom": {"description": "Cannot specify both product template and custom rule tree", "parameters": ["productId", "ruleTree"], "rule": "Use productId for template-based creation OR ruleTree for custom configuration"}}, "dns_record_creation": {"record_type_specific": {"description": "Record data format depends on record type", "cname_exclusivity": "CNAME records cannot coexist with other record types for same name", "mx_priority": "MX records require priority value in rdata"}}, "certificate_validation": {"validation_method": {"description": "Cannot specify multiple validation methods", "parameters": ["dns_challenges", "http_challenges"], "rule": "Choose either DNS-01 or HTTP-01 validation"}}}, "conditional_requirements": {"property_activation": {"email_notification": {"condition": "network == 'PRODUCTION'", "required_parameter": "notifyEmails", "business_rule": "Production activations require notification emails"}}, "dns_zone_creation": {"secondary_zone_masters": {"condition": "type == 'SECONDARY'", "required_parameter": "masters", "format": "Array of IPv4 addresses"}, "alias_zone_target": {"condition": "type == 'ALIAS'", "required_parameter": "target", "format": "Valid zone name"}}, "certificate_enrollment": {"san_certificates": {"condition": "certificateType == 'san'", "required_parameter": "csr.sans", "validation": "All SAN domains must be validated"}, "wildcard_certificates": {"condition": "certificateType == 'wildcard'", "validation_method": "DNS-01 only", "domain_pattern": "*.example.com format required"}}}, "parameter_inheritance": {"hierarchical_operations": {"property_to_versions": {"inherited_parameters": ["contractId", "groupId", "productId"], "description": "Property versions inherit organizational context"}, "zone_to_records": {"inherited_parameters": ["contractId", "zone"], "description": "Records inherit zone context for billing and management"}, "config_to_policies": {"inherited_parameters": ["contractId", "configId"], "description": "Security policies inherit configuration context"}}, "customer_context": {"multi_tenant_parameters": {"customer_section": {"description": "Specifies .edgerc section for multi-customer operations", "default": "default", "inheritance": "Propagates to all nested operations"}, "account_switch_key": {"description": "Account switching for partner/reseller access", "format": "Account key from Akamai Control Center", "scope": "All operations in same request context"}}}}}, "integration_patterns": {"workflow_sequences": {"new_property_workflow": {"description": "Complete workflow for creating and deploying a new property", "steps": [{"step": 1, "action": "List available contracts and groups", "endpoint": "/papi/v1/contracts, /papi/v1/groups", "parameters_obtained": ["contractId", "groupId"]}, {"step": 2, "action": "List available products", "endpoint": "/papi/v1/products", "parameters_used": ["contractId"], "parameters_obtained": ["productId"]}, {"step": 3, "action": "Create property", "endpoint": "/papi/v1/properties", "parameters_used": ["contractId", "groupId", "productId", "propertyName"], "parameters_obtained": ["propertyId"]}, {"step": 4, "action": "Create edge hostname", "endpoint": "/papi/v1/edgehostnames", "parameters_used": ["contractId", "groupId"], "parameters_obtained": ["edgeHostnameId"]}, {"step": 5, "action": "Associate hostname with property", "endpoint": "/papi/v1/properties/{propertyId}/versions/{version}/hostnames", "parameters_used": ["propertyId", "edgeHostnameId"]}, {"step": 6, "action": "Activate to staging", "endpoint": "/papi/v1/properties/{propertyId}/activations", "parameters_used": ["propertyId", "version", "network=STAGING"]}, {"step": 7, "action": "Activate to production", "endpoint": "/papi/v1/properties/{propertyId}/activations", "parameters_used": ["propertyId", "version", "network=PRODUCTION", "notifyEmails"]}]}, "certificate_integration_workflow": {"description": "Integrating certificates with property configuration", "steps": [{"step": 1, "action": "Create DV certificate enrollment", "endpoint": "/cps/v2/enrollments", "parameters_used": ["csr.cn", "adminContact", "techContact", "org"], "parameters_obtained": ["enrollmentId"]}, {"step": 2, "action": "Monitor validation challenges", "endpoint": "/cps/v2/enrollments/{enrollmentId}/dv-history", "polling_required": true}, {"step": 3, "action": "Create DNS validation records", "endpoint": "/config-dns/v2/changelists/{zone}/recordsets", "automation": "Can be automated for domains in Edge DNS"}, {"step": 4, "action": "Associate certificate with edge hostname", "endpoint": "/papi/v1/edgehostnames/{edgeHostnameId}", "parameters_used": ["enrollmentId"]}, {"step": 5, "action": "Activate property with certificate", "endpoint": "/papi/v1/properties/{propertyId}/activations", "validation": "Certificate deployment validates before activation"}]}}, "parameter_propagation": {"customer_context": {"description": "Customer parameter propagates through entire operation chain", "scope": "All API calls in single workflow inherit customer context", "authentication": "Uses customer-specific credentials from .edgerc"}, "organizational_hierarchy": {"description": "Contract and group context propagates to child resources", "inheritance_chain": "Contract > Group > Property/Zone > Versions/Records"}}}}